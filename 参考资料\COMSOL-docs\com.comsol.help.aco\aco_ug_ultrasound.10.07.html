<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Initial Values</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1597748">Initial Values</a></div>
    <div class="Body_text"><a name="1597751">The </a><span class="Menu-Bodytext">Initial Values </span>node adds initial values for the acoustic pressure and the acoustic velocity that can serve as an initial state for the solver. If more than one initial value is needed, from the <span class="Menu-Bodytext">Physics</span> toolbar click to add more <span class="Menu-Bodytext">Initial Values</span> nodes.</div>
    <div class="Head3"><a name="1597752">Initial Values</a></div>
    <div class="Body_text"><a name="1597753">Enter a value or expression for the initial values for the </a><span class="Menu-Bodytext">Pressure</span> <span class="EquationVariables">p</span> (SI unit: Pa) and the <span class="Menu-Bodytext">Acoustic velocity</span>, <span class="EquationBold">u</span> (SI unit: m/s).</div>
  </body>
</html>