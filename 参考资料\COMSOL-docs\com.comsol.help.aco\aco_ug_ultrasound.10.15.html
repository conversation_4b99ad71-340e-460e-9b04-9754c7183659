<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>General Interior Flux</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1621689">General Interior Flux</a></div>
    <div class="Body_text"><a name="1621716">The </a><span class="Menu-Bodytext">General Interior Flux</span> node, found under the <span class="Menu-Bodytext">More</span> submenu, adds a the most general interior boundary condition for the convected wave equation, as it is formulated (in the conservative form) for the discontinuous Galerkin method (dG). The condition defines the normal flux <span class="EquationBold">g</span> on an interior boundary by</div>
    <div class="Eqn"><a name="1621882"><img class="Default" src="images/aco_ug_ultrasound.10.15.1.png" width="79" height="21" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1621791">here </a><span class="Symbol">Γ</span><span class="EquationConstants">*</span> represents the total numerical flux. This means that if this condition is added on an interior boundary it overrides the existing Lax–Friedrichs numerical flux. So care should be taken when specifying this condition as it needs to include the necessary numerical flux contribution to ensure stability of the method.</div>
    <div class="Body_text"><a name="1622322">On an interior boundary you can use the </a><span class="Code">up() </span>and <span class="Code">down()</span> operators to access values from both sides of the boundary. If a dependent variable is used in this expression (without <span class="Code">up()</span> or <span class="Code">down()</span> operators) an implicit <span class="Code">mean()</span> operation is invoked taking the average of the up and down side values. Use the mesh normals (<span class="Code">nxmesh</span>,<span class="Code"> nymesh</span>, and <span class="Code">nzmesh</span> or, respectively, <span class="Code">nrmesh</span>,<span class="Code"> nphimesh</span>, and <span class="Code">nzmesh</span>) in the expression you define.</div>
    <div class="Head3"><a name="1621739">General Interior Flux</a></div>
    <div class="Body_text"><a name="1622329">Enter the expression for the components of the </a><span class="Menu-Bodytext">Flux vector </span><span class="EquationBold">g</span>.</div>
  </body>
</html>