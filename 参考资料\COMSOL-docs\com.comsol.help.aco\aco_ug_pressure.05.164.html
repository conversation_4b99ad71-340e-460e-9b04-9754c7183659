<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Characteristic Specific Impedance Models</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1395338">Characteristic Specific Impedance Models</a></div>
    <div class="Body_text"><a name="1395449">For well-defined wave types in infinite domains, an impedance condition exists in every point whereby the pressure and normal velocity are related. Thus, these impedances can be imposed on a boundary to model an infinite, open domain in cases where the wave source inside the domain is either a direction (plane wave), a point (spherical wave) or a line (cylindrical wave). However, be aware that only the boundary-normal component of the velocity is used in the impedance boundary condition while the tangential component is ignored; in cases of nonnegligible tangential components it is recommended to instead use the options </a><span class="Hyperlink"><a href="aco_ug_pressure.05.031.html#1389050" title="Plane Wave Radiation">Plane Wave Radiation</a></span>, <span class="Hyperlink"><a href="aco_ug_pressure.05.032.html#1389094" title="Spherical Wave Radiation">Spherical Wave Radiation</a></span>, and <span class="Hyperlink"><a href="aco_ug_pressure.05.033.html#1389121" title="Cylindrical Wave Radiation">Cylindrical Wave Radiation</a></span>.</div>
    <div class="Head4"><a name="1429299">Plane Wave</a></div>
    <div class="Body_text"><a name="1429921">The impedance is given by</a></div>
    <div class="Eqn"><a name="1429957"><img class="Default" src="images/aco_ug_pressure.05.164.1.png" width="66" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />,</a></div>
    <div class="Body_text"><a name="1430019">see </a><span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203183" title="References for the Pressure Acoustics Branch">Ref. 6</a></span>. This is given solely by material parameters and has no user input.</div>
    <div class="Head4"><a name="1429297">Spherical Wave</a></div>
    <div class="Body_text"><a name="1429981">This impedance corresponds to the wave from a point source. It is calculated from the expression (given in </a><span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203183" title="References for the Pressure Acoustics Branch">Ref. 6</a></span>)</div>
    <div class="Eqn"><a name="1430230"><img class="Default" src="images/aco_ug_pressure.05.164.2.png" width="279" height="61" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1430188">where </a><span class="EquationBold">x</span><span class="EquationConstantsSubscript">0</span> is the user-specified location of the point source generating the spherical waves, <span class="EquationBold">x</span> is the position of the boundary, and <span class="EquationVariables">r</span><span class="EquationConstantsSubscript">b</span> is the distance between the two.</div>
    <div class="Head4"><a name="1430574">Cylindrical Wave</a></div>
    <div class="Body_text"><a name="1430592">This classic infinite-domain wave impedance from an acoustic line source with the user-specified direction <img class="Default" src="images/aco_ug_pressure.05.164.3.png" width="20" height="24" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /> and position </a><span class="EquationBold">x</span><span class="EquationConstantsSubscript">0</span> is presented in, for example, <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203183" title="References for the Pressure Acoustics Branch">Ref. 6</a></span>. The expression for the impedance is</div>
    <div class="Eqn"><a name="1430945"><img class="Default" src="images/aco_ug_pressure.05.164.4.png" width="332" height="102" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1430939">where <img class="Default" src="images/aco_ug_pressure.05.164.5.png" width="171" height="29" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /> is the Hankel function of the second kind of order </a><span class="EquationVariables">m</span> given in terms of the Bessel functions of order <span class="EquationVariables">m</span> of the first and second kind, <span class="EquationVariables">J</span><span class="EquationConstantsSubscript">m</span>(<span class="EquationVariables">x</span>) and <span class="EquationVariables">Y</span><span class="EquationConstantsSubscript">m</span>(<span class="EquationVariables">x</span>), respectively. Notice that the source axis vector <span class="EquationBold">e</span><span class="EquationConstantsSubscript">sa</span> is automatically normalized in this implementation.</div>
  </body>
</html>