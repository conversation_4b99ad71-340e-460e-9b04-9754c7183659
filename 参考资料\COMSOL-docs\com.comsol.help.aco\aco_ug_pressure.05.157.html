<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Theory for the Exterior Field Calculation: The Helmholtz-Kirchhoff Integral</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1147817">Theory for the Exterior Field Calculation: The Helmholtz-Kirchhoff Integral</a></div>
    <div class="Body_text"><a name="1147938">The Acoustics Module has functionality to evaluate the acoustic pressure field in the exterior field region of the model (outside the computational domain). This section gives the relevant definitions and mathematical background as well as some general advice for analyzing the exterior field. Details about how to use the exterior field functionality is described in </a><span class="Hyperlink"><a href="aco_ug_pressure.05.025.html#814309" title="Exterior Field Calculation">Exterior Field Calculation</a></span> and in the modeling section <span class="Hyperlink"><a href="aco_ug_pressure.05.130.html#1403111" title="Evaluating the Acoustic Field in the Exterior: Near- and Far-Field">Evaluating the Acoustic Field in the Exterior: Near- and Far-Field</a></span>.</div>
    <div class="Head3"><a name="1147965">The Helmholtz-Kirchhoff Integral Representation</a></div>
    <div class="Body_text"><a name="1147966">In many cases, solving the acoustic Helmholtz equation everywhere in the domain where results are requested is neither practical nor necessary. For homogeneous media, the solution anywhere outside a closed surface containing all sources and scatterers can be written as a boundary integral in terms of quantities evaluated on the surface. To evaluate this </a><span class="Body_text-ital">Helmholtz-Kirchhoff</span>   integral, it is necessary to know both Dirichlet and Neumann values on the surface. Applied to acoustics, this means that if the pressure <span class="Body_text-ital">and</span> its normal derivative (which is related to the normal velocity) is known on a closed surface, the acoustic field can be calculated at any point outside.</div>
    <div class="Body_text"><a name="1147969">In general, the solution </a><span class="EquationVariables">p</span> to Helmholtz’ equation</div>
    <div class="Eqn"><a name="1147973"><img class="Default" src="images/aco_ug_pressure.05.157.01.png" width="123" height="21" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1147974">in the homogeneous domain exterior to a closed surface, </a><span class="EquationVariables">S</span>,<span class="EquationVariables"> </span>can be explicitly expressed in terms of the values of  <span class="EquationVariables">p</span> and its normal derivative on <span class="EquationVariables">S</span>:</div>
    <div class="Eqn"><a name="1147978"><img class="Default" src="images/aco_ug_pressure.05.157.02.png" width="300" height="48" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1147979">Here the coordinate vector </a><span class="EquationBold">r</span> parameterizes <span class="EquationVariables">S</span>. The unit vector <span class="EquationBold">n</span> is the outward normal to the exterior infinite domain; thus, <span class="EquationBold">n</span> points <span class="Body_text-ital">into</span>  the domain that <span class="EquationVariables">S</span> encloses. The function <span class="EquationVariables">G </span><span class="EquationConstants">(</span><span class="EquationBold">R</span><span class="EquationConstants">, </span><span class="EquationBold">r</span><span class="EquationConstants">)</span> is a Green’s function satisfying</div>
    <div class="Eqn"><a name="1147984"><img class="Default" src="images/aco_ug_pressure.05.157.03.png" width="256" height="25" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1147985">This essentially means that the Green’s function, seen as a function of  </a><span class="EquationBold">r</span>, is an outgoing traveling wave excited by a simple source at <span class="EquationBold">R</span>. In 3D, the Green’s function is therefore:</div>
    <div class="Eqn"><a name="1147989"><img class="Default" src="images/aco_ug_pressure.05.157.04.png" width="129" height="39" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1147991">In 2D, the Green’s function contains a Hankel function instead of the exponential:</a></div>
    <div class="Eqn"><a name="1147995"><img class="Default" src="images/aco_ug_pressure.05.157.05.png" width="168" height="36" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1147996">Inserting the 3D Green’s function in the general representation formula gives:</a></div>
    <div class="Equation"> (2-23)<a name="1148001"><img class="Default" src="images/aco_ug_pressure.05.157.06.png" width="398" height="48" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1148002">while in 2D, the Hankel function leads to a slightly different expression:</a></div>
    <div class="Equation"> (2-24)<a name="1148007"><img class="Default" src="images/aco_ug_pressure.05.157.07.png" width="437" height="58" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1148008">For axially symmetric geometries, the full 3D integral must be evaluated. The Acoustics Module uses an adaptive numerical quadrature in the azimuthal direction on a fictitious revolved geometry in addition to the standard mesh-based quadrature in the </a><span class="EquationVariables">rz</span>-plane.</div>
    <div class="Body_text"><a name="1148015">The default in the </a><span class="Hyperlink"><a href="aco_ug_pressure.05.025.html#814309" title="Exterior Field Calculation">Exterior Field Calculation</a></span> feature is to evaluate the full Helmholtz-Kirchhoff integral given in <span class="Hyperlink"><a href="#1148001" title="Theory for the Exterior Field Calculation: The Helmholtz-Kirchhoff Integral">Equation 2-23</a></span> and <span class="Hyperlink"><a href="#1148007" title="Theory for the Exterior Field Calculation: The Helmholtz-Kirchhoff Integral">Equation 2-24</a></span>.</div>
    <div class="Head3"><a name="1148021">The Far-Field Limit</a></div>
    <div class="Body_text"><a name="1148022">The full Helmholtz-Kirchhoff integral gives the pressure at any point at a finite distance from the source surface, but the numerical integration tends to lose accuracy at very large distances. At the same time, in some applications, the quantity of interest is the far-field radiation pattern, which can be defined as the limit of </a><span class="EquationVariables">r </span>| <span class="EquationVariables">p </span>|<span class="EquationVariables"> </span>when <span class="EquationVariables">r</span> goes to infinity in a given direction.</div>
    <div class="Body_text"><a name="1148023">Taking the limit of </a><span class="Hyperlink"><a href="#1148001" title="Theory for the Exterior Field Calculation: The Helmholtz-Kirchhoff Integral">Equation 2-23</a></span> when | <span class="EquationBold">R</span> | goes to infinity and ignoring the rapidly oscillating phase factor, the far field, <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">far</span> is defined as</div>
    <div class="Eqn"><a name="1148027"><img class="Default" src="images/aco_ug_pressure.05.157.08.png" width="308" height="54" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1148033"><img class="Default" src="images/aco_ug_pressure.05.157.09.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.00214488189pt">
          <div class="Body_text-middle-cell"><a name="1148035">The relevant quantity is | </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">far</span>| rather than <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">far</span> because the phase of the latter is undefined. For the same reason, only the direction of  <span class="EquationBold">R</span> is important, not its magnitude.</div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="1148037">Because Hankel functions asymptotically approach an exponential, the limiting 2D integral is remarkably similar to that in the 3D case:</a></div>
    <div class="Eqn"><a name="1148041"><img class="Default" src="images/aco_ug_pressure.05.157.10.png" width="319" height="54" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1148042">For axially symmetric geometries, the azimuthal integral of the limiting 3D case can be handled analytically, which leads to a rather complicated expression but avoids the numerical quadrature required in the general case. For zero azimuthal mode number </a><span class="EquationVariables">m</span><span class="Symbol"> = </span>0, the expression is:</div>
    <div class="Equation"> (2-25)<a name="1148047"><img class="Default" src="images/aco_ug_pressure.05.157.11.png" width="355" height="97" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1148048">In this integral, </a><span class="EquationVariables">r</span> and <span class="EquationVariables">z</span> are the radial and axial components of <span class="EquationBold">r</span>, while <span class="EquationVariables">R</span> and <span class="EquationVariables">Z</span> are the radial and axial components of <span class="EquationBold">R</span>.</div>
    <div class="Body_text"><a name="1148049">To evaluate the pressure in the far-field limit according to the equations in this section, set the </a><span class="Menu-Bodytext">Type of integral</span> option to the <span class="Menu-Bodytext">Far-field integral approximation for r </span><span class="Symbol">→</span> <span class="Symbol">∞ </span>option in the <span class="Menu-Bodytext">Exterior Field Calculation</span> section in the <span class="Menu-Bodytext">Settings</span> window for the feature. See <span class="Hyperlink"><a href="aco_ug_pressure.05.025.html#814309" title="Exterior Field Calculation">Exterior Field Calculation</a></span>.</div>
    <div class="Head3"><a name="1148055">The Elkernel Element</a></div>
    <div class="Body_text"><a name="1147930">These integrals can be implemented as integration coupling variables in COMSOL Multiphysics. However, such an approach is very inefficient because then the simple structure of the integration kernels cannot be exploited. In the Acoustics Module, convolution integrals of this type are therefore evaluated in optimized codes that hides all details from the user.</a></div>
  </body>
</html>