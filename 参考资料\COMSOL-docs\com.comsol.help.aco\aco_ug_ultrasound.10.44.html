<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Solving Highly Nonlinear Problems</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1647744">Solving Highly Nonlinear Problems</a></div>
    <div class="Body_text"><a name="1647829">When solving nonlinear problems the degree of nonlinearity, the number of harmonics generated and resolved, as well as the possibility of shock formation needs to be taken into account.</a></div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1648213">For nonlinear problems where only a few harmonics are generated the default physics and solver settings will work well.</a></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1648460">For highly nonlinear problems (with no shock formation), where more than a few harmonics are generated, it is recommended to make some changes to the Time-Explicit Solver. Change the </a><span class="Menu-Bodytext">Update time step</span> to <span class="Menu-Bodytext">Manual</span>, this will ensure that the local speed of sound is reevaluated and the internal time step is updated to ensure numerical stability.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1648465">For models that are highly nonlinear and exhibit shock formation, the use of the WENO limiter is necessary. This functionality is only available for linear discretization. Change the </a><span class="Menu-Bodytext">Element order</span> to <span class="Menu-Bodytext">Linear</span> in the <span class="Menu-Bodytext">Discretization</span> section on the physics interface level. Then, in the <span class="Menu-Bodytext">Limiter</span> section select <span class="Menu-Bodytext">WENO</span>. When limiters are used and the problem is highly nonlinear changes in the Time-Explicit Solver are also necessary. The computation of discontinuous solutions requires that a Strong Stability Preserving (SSP) Runge–Kutta method be used. The third order SSP Runge–Kutta method is achievable by changing the <span class="Menu-Bodytext">Order</span> of the Runge–Kutta method from the default 4 to 3. Moreover change the <span class="Menu-Bodytext">Update time step</span> to <span class="Menu-Bodytext">Manual</span>, this will ensure that the local speed of sound is reevaluated and the internal time step is update to ensure numerical stability. </div>
          </td>
        </tr>
      </table>
    </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1648589"><img class="Default" src="images/aco_ug_ultrasound.10.44.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.564651968504pt">
          <div class="Body_text"><a name="1648593">For an example of a highly nonlinear problem with shocks see the </a><span class="Body_text-ital">Nonlinear Propagation of a Cylindrical Wave — Verification Model</span> tutorial model. The Application Library path: <span class="Menu-Bodytext">Acoustics_Module/Nonlinear_Acoustics/nonlinear_cylindrical_wave</span></div>
        </td>
      </tr>
    </table>
  </body>
</html>