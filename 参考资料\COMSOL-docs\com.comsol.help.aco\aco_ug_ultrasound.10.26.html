<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Pressure</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1643818">Pressure</a></div>
    <div class="Body_text"><a name="1643864">The </a><span class="Menu-Bodytext">Pressure</span> node creates a boundary condition that acts as a pressure source at the boundary. The total acoustic pressure is given at the boundary <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span><span class="EquationVariables"> = p</span><span class="EquationConstantsSubscript">0</span><span class="Symbol">(</span><span class="EquationVariables">t</span><span class="Symbol">)</span>.</div>
    <div class="Head3"><a name="1643865">Pressure</a></div>
    <div class="Body_text"><a name="1643621">Enter an expression for the </a><span class="Menu-Bodytext">Pressure</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span>(t) (SI unit: Pa). The input can be any analytical function or interpolation data that can be function of both time and space.</div>
  </body>
</html>