<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Waveguide End Impedance Models</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1395299">Waveguide End Impedance Models</a></div>
    <div class="Body_text"><a name="1395443">Tubes and ducts are acoustic waveguides, and there are acoustic radiation losses when such a waveguide opens into a large domain. Idealized models for these losses have been implemented as boundary impedance models. Thus, instead of explicitly modeling the large domain, an appropriate impedance model </a><span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">end</span> can be applied with <span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">i</span> = <span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">end</span>. These models all assume that the domain is infinitely big, that the propagation is in the direction of the waveguide axis, and that the propagating mode is a plane wave. As with all other impedance boundary models, only the boundary-normal velocity component is taken into account.</div>
    <div class="Body_text"><a name="1787317">In the following the term </a><span class="Symbol">ρ</span><span class="EquationVariables">c</span> takes different values depending on when the impedance condition is applied. Specifically, for models with damping they are equal to the complex valued quantities <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">c</span><span class="EquationVariables">c</span><span class="EquationConstantsSubscript">c</span><span class="EquationVariables">, </span>while when applied on an Anisotropic Acoustic domain the normal direction variables are used <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">n</span><span class="EquationVariables">c</span><span class="EquationConstantsSubscript">n</span>.</div>
    <div class="Head4"><a name="1425629">Flanged Pipe, Circular</a></div>
    <div class="Body_text"><a name="1425662">For a pipe of a user-specified radius </a><span class="EquationVariables">a</span>, the acoustic losses are given by (see <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203183" title="References for the Pressure Acoustics Branch">Ref. 6</a></span>)</div>
    <div class="Eqn"><a name="1425855"><img class="Default" src="images/aco_ug_pressure.05.162.1.png" width="327" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1425849">where </a><span class="EquationVariables">J</span><span class="EquationConstantsSubscript">1</span>(<span class="EquationVariables">x</span>) is the Bessel function of the first kind of order 1, <span class="EquationVariables">H</span><span class="EquationConstantsSubscript">1</span>(<span class="EquationVariables">x</span>) is the Struve function, and <span class="EquationVariables">k</span> is the wave number of the wave. This expression is also known as the impedance from a baffled piston. </div>
    <div class="Head4"><a name="1426165">Flanged Pipe, Rectangular</a></div>
    <div class="Body_text"><a name="1426196">For a rectangular duct of user-specified inner width </a><span class="EquationVariables">w</span><span class="EquationConstantsSubscript">i</span> and inner height <span class="EquationVariables">h</span><span class="EquationConstantsSubscript">i</span>, the acoustic losses are given by (see <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203181" title="References for the Pressure Acoustics Branch">Ref. 5</a></span>)</div>
    <div class="Eqn"><a name="1426365"><img class="Default" src="images/aco_ug_pressure.05.162.2.png" width="391" height="112" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />.</a></div>
    <div class="Body_text"><a name="1426359">This relationship applies provided the following requirements are satisfied</a></div>
    <div class="Eqn"><a name="1426484"><img class="Default" src="images/aco_ug_pressure.05.162.3.png" width="204" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />.</a></div>
    <div class="Head4"><a name="1426547">Unflanged Pipe, Circular (Low </a><span class="EquationVariables">ka</span> Limit)</div>
    <div class="Body_text"><a name="1426553">For an unflanged circular pipe of a user-specified radius </a><span class="EquationVariables">a</span> in the limit of small radius (low <span class="EquationVariables">ka</span>), the pipe end impedance is given by the classical expression (see <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203183" title="References for the Pressure Acoustics Branch">Ref. 6</a></span>)</div>
    <div class="Eqn"><a name="1426670"><img class="Default" src="images/aco_ug_pressure.05.162.4.png" width="348" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />.</a></div>
    <div class="Head4"><a name="1426696">Unflanged Pipe, Circular</a></div>
    <div class="Body_text"><a name="1426726">For an unflanged pipe of any user-specified radius </a><span class="EquationVariables">a</span> relative to the wave number <span class="EquationVariables">k</span>, an approximate end impedance is given in <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1426940" title="References for the Pressure Acoustics Branch">Ref. 32</a></span>. It is </div>
    <div class="Eqn"><a name="1427270"><img class="Default" src="images/aco_ug_pressure.05.162.5.png" width="412" height="164" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1427228">where </a><span class="Symbol">δ</span>(<span class="EquationVariables">ka</span>) is a tabulated function reproducing the curve in Fig. 2 in <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1426940" title="References for the Pressure Acoustics Branch">Ref. 32</a></span> (where <span class="Symbol">δ</span>(<span class="EquationVariables">ka</span>) is referred to by <span class="EquationVariables">l</span>/<span class="EquationVariables">a</span>).</div>
  </body>
</html>