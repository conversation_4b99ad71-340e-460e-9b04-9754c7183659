<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Stabilizing Physical Instabilities (Filtering)</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1605274">Stabilizing Physical Instabilities (Filtering)</a></div>
    <div class="Body_text"><a name="1605313">In the </a><span class="Hyperlink"><a href="aco_ug_ultrasound.10.04.html#1603060" title="Convected Wave Equation Model">Convected Wave Equation Model</a></span> (at the domain level), it is possible to activate a filter similar to the one used in the absorbing layer. This filter can be used to stabilize and suppress physical instabilities that can occur in the solution. These are well known in linearize-Euler-like equations, see <span class="Hyperlink"><a href="aco_ug_ultrasound.10.52.html#1630183" title="References for the Ultrasound Interface">Ref. 6</a></span>. In the presence of a background flow, vorticity waves can occur; these are propagated with the background flow and are not acoustic waves.</div>
  </body>
</html>