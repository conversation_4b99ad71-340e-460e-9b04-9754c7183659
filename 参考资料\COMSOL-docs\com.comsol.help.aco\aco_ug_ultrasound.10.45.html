<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Adaptive Mesh Refinement</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1652438">Adaptive Mesh Refinement</a></div>
    <div class="Body_text"><a name="1653791">One challenge when solving nonlinear problems is that the mesh should potentially resolve many harmonics to get accurate solutions. To remedy this, adaptive mesh refinement technology can be used. The method will automatically refine the mesh to resolve large gradients, that is, sharp signal details that include several harmonics. The approach is useful for modeling the propagation of spatially localized signals like tone bursts or Gaussian pulses.</a></div>
    <div class="Body_text"><a name="1648375">To use the adaptive mesh refinement in a model follow these steps:</a></div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1654430">Mesh to resolve only the fundamental frequency in the model.</a></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1654526">In the</a><span class="Body_text-ital"> Time Dependent</span> study step, under the <span class="Menu-Bodytext">Adaptation</span> section, select <span class="Menu-Bodytext">Adaptive mesh refinement</span>. Then generate the default solver sequence.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1654638">Expand the solver tree and go to the </a><span class="Menu-Bodytext">Adaptive Mesh Refinement</span> node. Some changes need to be done here. For the <span class="Menu-Bodytext">Adaptation method</span> select <span class="Menu-Bodytext">General modifications</span>. Do not enable <span class="Menu-Bodytext">Allow coarsening</span> as the mesh needs to resolve the fundamental frequency. Finally, update the <span class="Menu-Bodytext">Error indicator</span> expression to use the pressure gradient norm. In a 2D axisymmetric model the expression is <span class="Code">sqrt(comp1.pr^2+comp1.pz^2)</span> and in a 3D model the expression is <span class="Code">sqrt(comp1.px^2+comp1.py^2+comp1.pz^2)</span>.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1655234">For highly localized signals, where the mesh adaptation generates a localized region with small elements, it can also be advantageous to switch from the default </a><span class="Menu-Bodytext">Runge-Kutta</span> (RK4) solver method to the <span class="Menu-Bodytext">Adams-Bashforth 3 (local)</span> method. </div>
          </td>
        </tr>
      </table>
    </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1652482"><img class="Default" src="images/aco_ug_ultrasound.10.45.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.564651968504pt">
          <div class="Body_text"><a name="1652484">For an example of a nonlinear problem with pulse propagation that uses mesh adaptation see the </a><span class="Body_text-ital">High-Intensity Focused Ultrasound (HIFU) Propagation Through a Tissue Phantom</span> tutorial model. The Application Library path: <span class="Menu-Bodytext">Acoustics_Module/Nonlinear_Acoustics/hifu_tissue_sample</span></div>
        </td>
      </tr>
    </table>
  </body>
</html>