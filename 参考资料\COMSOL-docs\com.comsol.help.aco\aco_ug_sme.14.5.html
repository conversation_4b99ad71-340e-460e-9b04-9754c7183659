<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Acoustic-Structure Multiphysics Interaction</title>
    <link rel="StyleSheet" href="css/aco_ug_sme.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="2402700">Acoustic-Structure Multiphysics Interaction</a></div>
    <div class="Body_text"><a name="2409838">As soon as an acoustics interface and a structural mechanics interface are present in the same model the </a><span class="Menu-Bodytext">Multiphysics </span>node appear, if there exist a predefined coupling between the two interfaces. Under the <span class="Menu-Bodytext">Multiphysics</span> node you can add the predefined multiphysics couplings that couple the fluid domain and to the solid domain. The couplings are described in detail in the <span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.01.html#548506" title="Multiphysics Couplings">Multiphysics Couplings</a></span> chapter.</div>
    <div class="Body_text"><a name="2409867">You can also find a set of predefined multiphysics interface that will automatically sets up the involved single physics and the multiphysics coupling. These are described in the </a><span class="Hyperlink"><a href="aco_ug_acousticstructure.07.01.html#1079791" title="Acoustic-Structure Interaction Interfaces">Acoustic-Structure Interaction Interfaces</a></span> chapter. Here you will also find modeling tips for acoustic-structure interaction problems in the <span class="Hyperlink"><a href="aco_ug_acousticstructure.07.11.html#1125858" title="Modeling with the Acoustic-Structure Interaction Branch">Modeling with the Acoustic-Structure Interaction Branch</a></span> section.</div>
    <div class="Body_text"><a name="2410350"> </a></div>
  </body>
</html>