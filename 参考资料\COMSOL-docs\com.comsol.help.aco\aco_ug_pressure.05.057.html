<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Incident Pressure Field (for Transient Models)</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1597274">Incident Pressure Field (for Transient Models)</a></div>
    <div class="Body_text"><a name="1597269">The </a><span class="Menu-Bodytext">Incident Pressure Field</span> node is a subnode to all nonreflecting boundary conditions (plane, cylindrical, and spherical wave radiation). From the <span class="Menu-Bodytext">Physics</span> toolbar, add to <span class="Hyperlink"><a href="aco_ug_pressure.05.031.html#1389050" title="Plane Wave Radiation">Plane Wave Radiation</a></span>, <span class="Hyperlink"><a href="aco_ug_pressure.05.032.html#1389094" title="Spherical Wave Radiation">Spherical Wave Radiation</a></span>, or <span class="Hyperlink"><a href="aco_ug_pressure.05.033.html#1389121" title="Cylindrical Wave Radiation">Cylindrical Wave Radiation</a></span> nodes. Three options exist for transient models: <span class="Menu-Bodytext">Plane wave (monochromatic)</span> (the default), <span class="Menu-Bodytext">Modulated Gaussian pulse</span>, and <span class="Menu-Bodytext">User defined</span>.</div>
    <div class="Head4"><a name="1597385">Plane Wave (Monochromatic)</a></div>
    <div class="Body_text"><a name="1598031">The same options apply as for the incident fields as for the </a><span class="Hyperlink"><a href="aco_ug_pressure.05.056.html#1595225" title="Background Pressure Field (for Transient Models)">Background Pressure Field (for Transient Models)</a></span> feature.</div>
    <div class="Head4"><a name="1743134">Modulated Gaussian Pulse</a></div>
    <div class="Body_text"><a name="1743130">The same options apply as for the incident fields as for the </a><span class="Hyperlink"><a href="aco_ug_pressure.05.056.html#1595225" title="Background Pressure Field (for Transient Models)">Background Pressure Field (for Transient Models)</a></span> feature.</div>
    <div class="Head4"><a name="1597967">User Defined</a></div>
    <div class="Body_text"><a name="1597968">For </a><span class="Menu-Bodytext">User defined</span> enter an expression that defined the incident wave. The incident pressure field needs to be defined as a traveling wave of the form</div>
    <div class="Eqn"><a name="1597972"><img class="Default" src="images/aco_ug_pressure.05.057.1.png" width="76" height="20" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1597963">where </a><span class="Symbol">ω</span> is the angular frequency and <span class="EquationBold">k</span> is the wave vector. The function <span class="EquationVariables">f</span> is any function, for example, a sine function. This spatial and temporal dependency is a requirement for the radiation boundary condition to work properly.</div>
  </body>
</html>