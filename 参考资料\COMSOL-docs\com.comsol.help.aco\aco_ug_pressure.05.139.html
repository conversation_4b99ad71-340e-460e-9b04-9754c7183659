<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Postprocessing BEM Results</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1635971">Postprocessing BEM Results</a></div>
    <div class="Body_text"><a name="1635972">When solving a problem with </a><span class="Hyperlink"><a href="aco_ug_pressure.05.069.html#1576376" title="The Pressure Acoustics, Boundary Elements Interface">The Pressure Acoustics, Boundary Elements Interface</a></span>, the resulting solution will consist of the dependent variables on boundaries. Evaluating the solution in a domain is based on a kernel evaluation.</div>
    <div class="Body_text"><a name="1646654">On boundaries, dependent variables exist for the pressure </a><span class="Code">p</span>, up and down pressure-dependent variables on interior boundaries (<span class="Code">pabe.p_up</span> and <span class="Code">pabe.p_down</span>), as well as a normal flux variable <span class="Code">pabe.pbam1.bemflux</span>.</div>
    <div class="Body_text"><span class="Hyperlink"><a href="aco_ug_pressure.05.069.html#1576376" title="The Pressure Acoustics, Boundary Elements Interface" name="1646715">The Pressure Acoustics, Boundary Elements Interface</a></span> sets up predefined postprocessing variables that combine the properties of the boundary variables, when needed, with variables based on the kernel evaluation.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1747024"><img class="Default" src="images/aco_ug_pressure.05.139.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 290.466283464567pt">
          <div class="Body_text-middle-cell"><a name="1747026">The BEM solution can be evaluated at a given coordinate using the </a><span class="Code">at3_spatial(...)</span> operator. In a 3D model you can evaluate the sound pressure level in the point (<span class="EquationVariables">x</span>,<span class="EquationVariables">y</span>,<span class="EquationVariables">z</span>) = (1 m,0,0) by typing:<br /><span class="Code">at3_spatial(1[m],0,0,pabe.Lp,’minc’).</span></div>
          <div class="Body_text-cell"><a name="1747500">This can, for example, be used to normalize the data in a </a><span class="Menu-Bodytext">Radiation Pattern</span> plot by typing:<br /><span class="Code">pabe.Lp-at3_spatial(1[m],0,0,pabe.Lp,’minc’)</span></div>
        </td>
      </tr>
    </table>
    <div class="Head4"><a name="1647528">Domain Evaluation</a></div>
    <div class="Body_text"><a name="1647525">The BEM solution can in general be evaluated in domains using the </a><span class="Menu-Bodytext">Grid 2D</span> and <span class="Menu-Bodytext">Grid 3D</span> datasets. This sets up an underlying grid for the kernel evaluation of the solution. The grid dataset can, for example, be selected as the input <span class="Menu-Bodytext">Dataset</span> for a slice plot. A grid dataset and a multislice plot are generated and used in the default plots when a BEM model is solved. Note also that the kernel-based variables can be evaluated on domains that are meshed.</div>
    <div class="Body_text"><a name="1647679">All the </a><span class="Hyperlink"><a href="aco_ug_pressure.05.131.html#1506222" title="Dedicated Acoustics Plots for Postprocessing">Dedicated Acoustics Plots for Postprocessing</a></span> that exist for the FEM interfaces can be used with the variables defined by the BEM solution. This is, for example, the <span class="Menu-Bodytext">Radiation Pattern</span> plot or the <span class="Menu-Bodytext">Directivity</span> plot used for analyzing the spatial radiation pattern of a transducer or a scattered field.</div>
    <div class="Head4"><a name="1647522">Boundary Evaluation</a></div>
    <div class="Body_text"><a name="1646910">When evaluating the solution on boundaries, the plot lists are populated with special boundary variables in a </a><span class="Menu-Bodytext">Boundary variables</span> submenu located under, for example, the <span class="Menu-Bodytext">Pressure and sound pressure level</span> or <span class="Menu-Bodytext">Acceleration and velocity</span> menu. The boundary variables have a “<span class="Code">_bnd</span>” added to their names. For example, the total acoustic pressure on a boundary is <span class="Code">pabe.p_t_bnd</span>. On interior boundaries, there are also up and down variants of the variables, with “<span class="Code">_up</span>” and “<span class="Code">_down</span>” added, respectively. On interior boundaries (where an interior boundary condition has been added), the boundary variable is the average of the up and down variables.</div>
    <div class="Body_text"><a name="1648724">The boundary variables should be used when plotting on surfaces, as they do not rely on the kernel evaluation but are directly related to the DOFs solved for in the BEM problem.</a></div>
  </body>
</html>