<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Formulation for Eigenfrequency Studies</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="921416">Formulation for Eigenfrequency Studies</a></div>
    <div class="Body_text"><a name="921518">When performing an eigenfrequency study, the governing equations (</a><span class="Hyperlink"><a href="aco_ug_thermo.09.49.html#920646" title="Acoustic Perturbation and Linearization">Equation 6-7</a></span>) are on the form:</div>
    <div class="Eqn"><a name="922018"><img class="Default" src="images/aco_ug_thermo.09.51.1.png" width="411" height="116" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="922382">where the eigenvalue is </a><span class="Symbol">λ</span> = <span class="Symbol">−</span><span class="EquationVariables">i</span><span class="Symbol">ω</span>. It is important to note that there is a difference between regular pressure acoustics and thermoviscous acoustics in terms of what modes can exist and which modes are found during an eigenfrequency study. In pressure acoustics only the pure acoustic modes exist; the equations and assumptions made ensure this. In thermoviscous acoustics, on the other hand, the equations are formulated for all small-signal components that can exist. This means that other nonacoustic modes also exist — that is, thermal and vorticity modes.</div>
    <div class="Head3"><a name="921535">Vorticity and thermal modes</a></div>
    <div class="Body_text"><a name="921570">When solving an eigenfrequency problem in thermoviscous acoustics, it is important to take a close look at the obtained eigenfrequencies and assess if they are acoustic or not. The nature of the solution is of the form</a></div>
    <div class="Eqn"><a name="924334"><img class="Default" src="images/aco_ug_thermo.09.51.2.png" width="400" height="25" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="943852">where </a><span class="Symbol">λ</span> is the eigenvalue. Typically, eigenvalues exist near the positive real axis, where <span class="Symbol">β ≈ </span><span class="EquationConstants">0</span>. These are exponentially decaying nonacoustic (nonoscillating) modes that stem from the thermal equation or the deviatoric part of the momentum equation (the nonpressure and nonvolume part of the stress tensor) also called the vorticity modes. The acoustic eigenvalues on the other hand lie close to the imaginary axis and are oscillating and slightly damped.</div>
    <div class="Head3"><a name="922053">Other Spurious Modes</a></div>
    <div class="Body_text"><a name="922079">Note that other spurious and nonacoustic modes can also exist when, for example, a PML layer is used to model an open boundary. These modes stem from nonphysical phenomena and the scaling inside the PML layer. In all cases, it is a good idea to have an a priori knowledge of the location and type of the eigenvalues, maybe from solving an lossless pressure acoustics model, and also to look at the modes in terms of, for example, the pressure field.</a></div>
  </body>
</html>