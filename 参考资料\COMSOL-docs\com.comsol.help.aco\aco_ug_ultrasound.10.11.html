<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Acoustic Impedance</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1602886">Acoustic Impedance</a></div>
    <div class="Body_text"><a name="1608424">The </a><span class="Menu-Bodytext">Acoustic Impedance </span>node adds a boundary condition defining a relation between the local acoustic pressure <span class="EquationVariables">p</span> and the normal acoustic velocity <span class="EquationBold">n</span>·<span class="EquationBold">u</span>, that is, the acoustic impedance Z. The condition specifies the inward normal velocity</div>
    <div class="Eqn"><a name="1610516"><img class="Default" src="images/aco_ug_ultrasound.10.11.1.png" width="82" height="36" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1610573">This condition can be used to model the properties of artificial boundaries. When the impedance is set equal to the characteristic specific impedance of a propagating plane wave </a><span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span>c<span class="EquationConstantsSubscript">0</span>, the condition represent the simplest nonreflecting boundary condition. This is also the default value of the impedance when the impedance condition is added.</div>
    <div class="Body_text"><a name="1610108">Note that this condition is not equivalent to the general impedance condition, as given by Myers (see </a><span class="Hyperlink"><a href="aco_ug_ultrasound.10.52.html#1629923" title="References for the Ultrasound Interface">Ref. 4</a></span>), when a flow is present. This means that, for example, it cannot be used at a reacting surface which has a tangential flow. For this kind of detailed conditions a frequency domain representation is needed.</div>
    <div class="Body_text"><a name="1611288">The acoustic impedance condition (with the default value </a><span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span>c<span class="EquationConstantsSubscript">0</span>) should be used at the outer boundary when <span class="Hyperlink"><a href="aco_ug_ultrasound.10.39.html#1605122" title="Absorbing Layers">Absorbing Layers</a></span> are used in a model.</div>
    <div class="Head3"><a name="1610600">Acoustic Impedance</a></div>
    <div class="Body_text"><a name="1610576">Enter a value or expression for the </a><span class="Menu-Bodytext">Acoustic Impedance </span><span class="EquationVariables">Z</span> (SI unit: Pa·s/m). The default expression is <span class="Code">cwe.c0*cwe.rho0</span> which is the characteristic specific impedance <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span>c<span class="EquationConstantsSubscript">0</span> of a propagating plane wave.</div>
  </body>
</html>