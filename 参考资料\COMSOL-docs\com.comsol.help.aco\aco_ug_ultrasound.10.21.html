<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Mass Source</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1646172">Mass Source</a></div>
    <div class="Body_text"><a name="1646173">The </a><span class="Menu-Bodytext">Mass Source</span> sets up the right-hand side of the continuity equation, in <span class="Hyperlink"><a href="aco_ug_ultrasound.10.18.html#1646263" title="Nonlinear Pressure Acoustics, Time Explicit Model">Equation 7-1</a></span>, in terms of a mass rate of change. This is a monopole-like source that acts uniformly in all directions.</div>
    <div class="Eqn"><a name="1646180"><img class="Default" src="images/aco_ug_ultrasound.10.21.1.png" width="70" height="36" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Head3"><a name="1646181">Mass Source</a></div>
    <div class="Body_text"><a name="1646182">Enter the </a><span class="Menu-Bodytext">Mass source </span><span class="EquationVariables">M</span> (SI unit: kg/(m<span class="EquationConstantsSuperscript">3</span>·s)).</div>
  </body>
</html>