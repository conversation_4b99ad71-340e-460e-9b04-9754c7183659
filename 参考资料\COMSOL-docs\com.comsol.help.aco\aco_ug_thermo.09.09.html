<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Wall</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="952063">Wall</a></div>
    <div class="Body_text"><a name="952065">Use the </a><span class="Menu-Bodytext">Wall</span> node to model the most common conditions at solid surfaces. This is the default boundary condition. This condition contains both a mechanical and a thermal selection. The default is a no-slip and isothermal condition applicable in most cases. More advanced conditions at boundaries can be set up by combining any of the <span class="Menu-Bodytext">Mechanical</span> and <span class="Menu-Bodytext">Thermal </span>conditions available.</div>
    <div class="Body_text"><a name="1029031">The </a><span class="Menu-Bodytext">Wall</span> condition is also the default condition added when the physics interface is set up. Note that the selections in this default condition can be changed (they are not locked to all boundaries). When used together with a perfectly matched layer (PML) it is recommended to use the no-slip option at the outer boundary of the layer. Specifically, using the slip condition in combination with a rational scaling in the PML will lead to a singularity and an error.</div>
    <div class="Body_text"><a name="1014640">The no-slip condition is the origin of the viscous boundary layer and the isothermal condition is the origin of the thermal boundary layer. It is within these acoustic boundary layers that the main dissipation happens. See the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.46.html#1008569" title="Theory Background for the Thermoviscous Acoustics Branch">Theory Background for the Thermoviscous Acoustics Branch</a></span> section for more details.</div>
    <div class="Head3"><a name="959222">Mechanical</a></div>
    <div class="Body_text"><a name="959310">Select a </a><span class="Menu-Bodytext">Mechanical condition</span> — <span class="Menu-Bodytext">No slip</span> (the default) or <span class="Menu-Bodytext">Slip</span>. See the <span class="Hyperlink"><a href="aco_ug_thermo.09.18.html#959638" title="No Slip">No Slip</a></span> and <span class="Hyperlink"><a href="aco_ug_thermo.09.19.html#816697" title="Slip">Slip</a></span> conditions for further details.</div>
    <div class="Head3"><a name="959295">Thermal</a></div>
    <div class="Body_text"><a name="959383">Select a </a><span class="Menu-Bodytext">Thermal condition</span> — <span class="Menu-Bodytext">Isothermal</span> (the default) or <span class="Menu-Bodytext">Adiabatic</span>. See the <span class="Hyperlink"><a href="aco_ug_thermo.09.24.html#1001487" title="Isothermal">Isothermal</a></span> and <span class="Hyperlink"><a href="aco_ug_thermo.09.25.html#816916" title="Adiabatic">Adiabatic</a></span> conditions for further details.</div>
    <div class="Head3"><a name="952066">Constraint Settings</a></div>
    <div class="Body_text"><a name="961656">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.09.1.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. If <span class="Menu-Bodytext">Slip</span> is selected for the <span class="Menu-Bodytext">Mechanical condition</span> and <span class="Menu-Bodytext">Use weak constraints</span> is enabled, then the default discontinuous Galerkin (penalty like) formulation is switched to a Lagrange multiplier formulation instead. See the <span class="Hyperlink"><a href="aco_ug_thermo.09.19.html#816697" title="Slip">Slip</a></span> condition for details. For the <span class="Menu-Bodytext">No-slip</span> and <span class="Menu-Bodytext">Isothermal</span> selections a weak formulation is used instead of a pointwise constraint.</div>
    <div class="Head3"><a name="1046376">Excluded Edges/Points</a></div>
    <div class="Body_text"><a name="1046380">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.09.2.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. See <span class="Hyperlink"><a href="aco_ug_thermo.09.45.html#1039488" title="Suppressing Constraints on Lower Dimensions">Suppressing Constraints on Lower Dimensions</a></span> for details.</div>
  </body>
</html>