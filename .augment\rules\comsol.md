---
type: "always_apply"
---

我们正在python环境中使用MPh库与COMSOL Multiphysics®应用程序交互，进行物理学仿真计算工作。
- python environment directory: "E:\XiGPrograms\anaconda\base\envs\comsol"
- Python interpreter version: 3.8.20
- Some installed packages: MPh, JPype, NumPy
- COMSOL installation directory: "E:\XiGPrograms\comsol\base"
- COMSOL version: 6.0

请积极查阅"参考资料"文件夹。
- 其中，"参考资料\MPh-main"是MPh库的源代码仓库Clone，其中包含文档（"…\docs"）等有用的信息。
- 而"参考资料\COMSOL-docs"是COMSOL Multiphysics®应用程序（及其所有物理场模块）的HTML格式使用说明文档，其内容量堪称巨大，请活用搜索相关功能来寻找你需要的信息。