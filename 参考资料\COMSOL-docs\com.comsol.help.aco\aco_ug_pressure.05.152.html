<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Pressure Acoustics, Frequency Domain Equations</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1176795">Pressure Acoustics, Frequency Domain Equations</a></div>
    <div class="Body_text"><span class="Hyperlink"><a href="aco_ug_pressure.05.002.html#630838" title="The Pressure Acoustics, Frequency Domain Interface" name="1176797">The Pressure Acoustics, Frequency Domain Interface</a></span> exists for several types of studies. Here the equations are presented for the frequency domain, eigenfrequency, and mode analysis studies. All the interfaces solve for the acoustic pressure  <span class="EquationVariables">p</span>. It is available in all space dimensions — for 3D, 2D, and 1D Cartesian geometries as well as for 2D and 1D axisymmetric geometries.</div>
    <div class="Head3"><a name="1179093">Frequency Domain</a></div>
    <div class="Body_text"><a name="1179082">The frequency domain, or time-harmonic, formulation uses the inhomogeneous Helmholtz equation:</a></div>
    <div class="Equation"> (2-16)<a name="1176802"><img class="Default" src="images/aco_ug_pressure.05.152.01.png" width="210" height="51" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1176803">This is </a><span class="Hyperlink"><a href="aco_ug_pressure.05.151.html#1060354" title="The Governing Equations">Equation 2-14</a></span> repeated with the introduction of the wave number <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">eq</span> used in the equations. It contains both the ordinary wave number <span class="EquationVariables">k</span> as well as out-of-plane and azimuthal (or circumferential) contributions, when applicable. Note also that the pressure is here the total pressure <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span> which is the sum of a possible <span class="Hyperlink"><a href="aco_ug_pressure.05.008.html#1054320" title="Background Pressure Field">Background Pressure Field</a></span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span> and the scattered field <span class="EquationVariables">p</span><span class="EquationBoldSubscript">s</span>. This enables for a so-called scattered field formulation of the equations. If no background field is present <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span><span class="EquationVariables"> </span>= <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">s</span> = <span class="EquationVariables">p</span>.</div>
    <div class="Body_text"><a name="1179569">In this equation, </a><span class="EquationVariables">p </span><span class="Symbol">=</span><span class="EquationVariables"> p </span><span class="EquationConstants">(</span><span class="EquationBold">x</span>,<span class="Symbol">ω</span><span class="EquationConstants">) = </span><span class="EquationVariables">p</span><span class="EquationConstants">(</span><span class="EquationBold">x</span><span class="EquationConstants">)e</span><span class="EquationVariablesSuperscript">i</span><span class="Superscript-Symbol">ω</span><span class="EquationVariablesSuperscript">t</span><span class="EquationConstants"> (</span>the dependence on <span class="Symbol">ω</span> is henceforth not explicitly indicated<span class="EquationConstants">). </span>Compute the frequency response by doing a parametric sweep over a frequency range using harmonic loads and sources.</div>
    <div class="Body_text"><a name="1176804">When there is damping, </a><span class="Symbol">ρ</span><span class="EquationVariablesSubscript">c</span> and <span class="EquationVariables">c</span><span class="EquationConstantsSubscript">c</span> are complex-valued quantities. The available damping models and how to apply them is described in the sections <span class="Hyperlink"><a href="aco_ug_pressure.05.004.html#1146156" title="Pressure Acoustics">Pressure Acoustics</a></span> and <span class="Hyperlink"><a href="aco_ug_pressure.05.167.html#1128651" title="Theory for the Equivalent Fluid Models">Theory for the Equivalent Fluid Models</a></span>.</div>
    <div class="Body_text"><span class="Hyperlink"><a href="#1176802" title="Pressure Acoustics, Frequency Domain Equations" name="1176814">Equation 2-16</a></span> is the equation that the software solves for 3D geometries. In lower-dimensional and axisymmetric cases, restrictions on the coordinate dependence mean that the equations differ from case to case. Here is a brief summary of the situation.</div>
    <div class="Head4"><a name="1176815">2D</a></div>
    <div class="Body_text"><a name="1176816">In 2D, the pressure is of the form</a></div>
    <div class="Eqn"><a name="1176820"><img class="Default" src="images/aco_ug_pressure.05.152.02.png" width="126" height="28" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1176824">which inserted in </a><span class="Hyperlink"><a href="#1176802" title="Pressure Acoustics, Frequency Domain Equations">Equation 2-16</a></span> gives</div>
    <div class="Equation"> (2-17)<a name="1176829"><img class="Default" src="images/aco_ug_pressure.05.152.03.png" width="211" height="96" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1176832">The </a><span class="Body_text-ital">out-of-plane wave number</span> <span class="EquationVariables">k</span><span class="EquationVariablesSubscript">z</span> can be set on the Pressure Acoustics page. By default its value is 0. In the mode analysis study <span class="Symbol">−</span><span class="EquationVariables">ik</span><span class="EquationVariablesSubscript">z</span> is used as the eigenvalue <span class="Symbol">λ</span>.</div>
    <div class="Head4"><a name="1176833">2D Axisymmetry</a></div>
    <div class="Body_text"><a name="1176834">For 2D axisymmetric geometries the independent variables are the radial coordinate </a><span class="EquationVariables">r</span> and the axial coordinate <span class="EquationVariables">z</span>. The only dependence allowed on the azimuthal coordinate <span class="Symbol"><img class="Default" src="images/aco_ug_pressure.05.152.04.png" width="14" height="20" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></span> is through a phase factor,</div>
    <div class="Equation"> (2-18)<a name="1176843"><img class="Default" src="images/aco_ug_pressure.05.152.05.png" width="155" height="25" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1176846">where </a><span class="EquationVariables">m</span> denotes the <span class="Body_text-ital">azimuthal mode number</span>. The mode number defines an <span class="Body_text-ital">azimuthal wave number</span> <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">m</span> = <span class="EquationVariables">m</span>/<span class="EquationVariables">r</span>. Because the azimuthal coordinate is periodic, <span class="EquationVariables">m</span> must be an integer. Just like <span class="EquationVariables">k</span><span class="EquationVariablesSubscript">z</span> in the 2D case, <span class="EquationVariables">m</span> can be set in the Settings window for Pressure Acoustics.</div>
    <div class="Body_text"><a name="1176850">As a result of </a><span class="Hyperlink"><a href="#1176843" title="Pressure Acoustics, Frequency Domain Equations">Equation 2-18</a></span>, the equation to solve for the acoustic pressure in 2D axisymmetric geometries becomes</div>
    <div class="Eqn"><a name="1176854"><img class="Default" src="images/aco_ug_pressure.05.152.06.png" width="331" height="94" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Head4"><a name="1176855">1D Axisymmetry</a></div>
    <div class="Body_text"><a name="1176856">In 1D axisymmetric geometries,</a></div>
    <div class="Eqn"><a name="1176860"><img class="Default" src="images/aco_ug_pressure.05.152.07.png" width="175" height="28" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1176861">leading to the radial equation</a></div>
    <div class="Eqn"><a name="1176865"><img class="Default" src="images/aco_ug_pressure.05.152.08.png" width="227" height="94" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1176867">where both the </a><span class="Body_text-ital">azimuthal wave number</span> <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">m</span> and the <span class="Body_text-ital">axial wave number</span> <span class="EquationVariables">k</span><span class="EquationVariablesSubscript">z</span>, appear as parameters.</div>
    <div class="Head4"><a name="1176868">1D</a></div>
    <div class="Body_text"><a name="1176869">The equation for the 1D case is obtained by letting the pressure depend on a single Cartesian coordinate </a><span class="EquationVariables">x</span>:</div>
    <div class="Eqn"><a name="1176873"><img class="Default" src="images/aco_ug_pressure.05.152.09.png" width="195" height="94" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Head3"><a name="1176875">Eigenfrequency</a></div>
    <div class="Body_text"><a name="1176877">In the eigenfrequency formulation, the source terms are absent; the eigenmodes and eigenfrequencies are solved for:</a></div>
    <div class="Equation"> (2-19)<a name="1176882"><img class="Default" src="images/aco_ug_pressure.05.152.10.png" width="150" height="52" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1176883">The eigenvalue </a><span class="Symbol">λ</span> introduced in this equation is related to the eigenfrequency <span class="EquationVariables">f</span>, and the angular frequency <span class="Symbol">ω</span>, through <span class="Symbol">λ = </span><span class="EquationVariables">i</span><span class="EquationConstants">2</span><span class="Symbol">π</span><span class="EquationVariables">f</span><span class="Symbol"> = </span><span class="EquationVariables">i</span><span class="Symbol">ω</span>. Because they are independent of the pressure, the solver ignores any dipole and monopole sources unless a coupled eigenvalue problem is being solved.</div>
    <div class="Body_text"><span class="Hyperlink"><a href="#1176882" title="Pressure Acoustics, Frequency Domain Equations" name="1176887">Equation 2-19</a></span> applies to the 3D case. The equations solved in eigenfrequency studies in lower dimensions and for axisymmetric geometries are obtained from their time-harmonic counterparts, given in the previous subsection, by the substitution <span class="Symbol">ω</span><span class="EquationConstantsSuperscript">2</span><span class="Symbol"> → −λ</span><span class="EquationConstantsSuperscript">2</span>.</div>
    <div class="Body_text"><a name="1176888">Switch between specifying the eigenvalues, the eigenfrequencies, and the angular frequencies by selecting from the Eigenvalue transformation list in the solver sequence’s Settings window for Eigenvalue.</a></div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1176895"><img class="Default" src="images/aco_ug_pressure.05.152.11.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.564651968504pt">
          <div class="Body_text-middle-cell"><span class="Body_text-ital"><a name="1176901">Vibrations of a Disk Backed by an Air-Filled Cylinder</a></span>: Application Library path <span class="Menu-Bodytext">Acoustics_Module/Verification_Examples/coupled_vibrations</span></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1176903">Mode Analysis in 2D and 1D axisymmetric</a></div>
    <div class="Body_text"><a name="1176907">See </a><span class="Hyperlink"><a href="aco_ug_pressure.05.155.html#641237" title="Pressure Acoustics, Boundary Mode Equations">Mode Analysis Study</a></span> in the <span class="Hyperlink"><a href="aco_ug_pressure.05.155.html#805702" title="Pressure Acoustics, Boundary Mode Equations">Pressure Acoustics, Boundary Mode Equations</a></span> section. The mode analysis study type is only available for the Pressure Acoustics, Frequency Domain interface in 2D and 1D axisymmetric components, where the solver solves for the eigenvalues <span class="Symbol">λ </span>=<span class="Symbol"> −</span><span class="EquationVariables">ik</span><span class="EquationVariablesSubscript">z</span> for a given frequency. Here <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">z</span> is the out-of-plane wave number of a given mode and the resulting pressure field <span class="EquationVariables">p</span> represents the mode on the cross section of an infinite waveguide or duct.</div>
  </body>
</html>