<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Thermoviscous Acoustics Model</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="985552">Thermoviscous Acoustics Model</a></div>
    <div class="Body_text"><a name="985554">Use the </a><span class="Menu-Bodytext">Thermoviscous Acoustics Model</span> node to define the model inputs (the background equilibrium temperature and pressure) and the material properties of the fluid (dynamic viscosity, bulk viscosity, thermal conductivity, heat capacity at constant pressure, and equilibrium density) necessary to model the propagation of acoustic compressible waves in a thermoviscous acoustic context. Extended inputs are available for the coefficient of thermal expansion and the compressibility, which enables modeling of any constitutive relation for the fluid.</div>
    <div class="Body_text"><a name="996470">The </a><span class="Menu-Bodytext">Model Inputs</span>, the <span class="Menu-Bodytext">Thermoviscous Acoustics Model</span>, and the <span class="Menu-Bodytext">Thermal Expansion and Compressibility</span> sections are the same as for the frequency domain and transient interfaces. They are described under the <span class="Hyperlink"><a href="aco_ug_thermo.09.04.html#950471" title="Thermoviscous Acoustics Model">Thermoviscous Acoustics Model</a></span> section in <span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#954726" title="The Thermoviscous Acoustics, Frequency Domain Interface">The Thermoviscous Acoustics, Frequency Domain Interface</a></span>.</div>
    <div class="Body_text"><a name="997230">Note that for the boundary mode interface it is equally important as for the frequency domain and transient interfaces to set the material parameters correctly. The values of the coefficient of thermal expansion and the isothermal compressibility are prone to errors, so make sure to take a thorough look at the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.04.html#912241" title="Thermoviscous Acoustics Model">Thermal Expansion and Compressibility</a></span> section.</div>
    <div class="Head3"><a name="997231">Boundary Mode Settings</a></div>
    <div class="Body_text"><a name="997232">This section contains an input for the </a><span class="Menu-Bodytext">Propagation direction</span> <span class="EquationBold">n</span> of the modes analyzed with the interface. Typically, this direction is equal to the surface or boundary normal. The default value is (<span class="Code">tabm.nx</span>, <span class="Code">tabm,ny</span>, <span class="Code">tabm,nz</span>) in 3D and (<span class="Code">tabm.nr</span>, <span class="Code">tabm.nz</span>) in 2D axisymmetric. When setting up models, this vector defines the positive propagation direction for the waves and thus the sign of the wave number.</div>
  </body>
</html>