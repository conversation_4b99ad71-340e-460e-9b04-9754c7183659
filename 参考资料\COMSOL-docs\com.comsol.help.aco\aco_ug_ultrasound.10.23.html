<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Volume Force Source</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1646207">Volume Force Source</a></div>
    <div class="Body_text"><a name="1646208">The </a><span class="Menu-Bodytext">Volume Force Source </span>sets up the right-hand side of the momentum equation, in <span class="Hyperlink"><a href="aco_ug_ultrasound.10.18.html#1646263" title="Nonlinear Pressure Acoustics, Time Explicit Model">Equation 7-1</a></span>, in terms of a volumetric force. This is a dipole-like source that acts differently in two directions.</div>
    <div class="Eqn"><a name="1646215"><img class="Default" src="images/aco_ug_ultrasound.10.23.1.png" width="54" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Head3"><a name="1646216">Volume Force Source</a></div>
    <div class="Body_text"><a name="1646217">Enter the </a><span class="Menu-Bodytext">Volume force source </span><span class="EquationBold">F</span> (SI unit: N/m<span class="Superscript-Bodytext">3</span>).</div>
  </body>
</html>