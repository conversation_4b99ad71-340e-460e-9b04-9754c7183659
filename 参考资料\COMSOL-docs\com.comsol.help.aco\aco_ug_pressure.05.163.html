<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Porous Layer Models</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1395324">Porous Layer Models</a></div>
    <div class="Body_text"><a name="1395446">A boundary impedance model is implemented to specifically handle cases investigating the acoustical properties of a porous layer of a given thickness </a><span class="EquationVariables">d</span> backed by a sound-hard wall. This can be applied instead of explicitly modeling the porous layer, as long as the incident acoustic field is normal to the boundary; as for all the other boundary impedance models, the tangential components of the acoustic field are ignored by this model.</div>
    <div class="Body_text"><a name="1428073">For a porous layer with a user-specified thickness </a><span class="EquationVariables">d</span>, the impedance from the porous layer backed by a sound-hard wall is given by (see <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1305788" title="References for the Pressure Acoustics Branch">Ref. 9</a></span>)</div>
    <div class="Eqn"><a name="1428315"><img class="Default" src="images/aco_ug_pressure.05.163.1.png" width="206" height="40" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />.</a></div>
    <div class="Body_text"><a name="1428322">Here </a><span class="Symbol">ρ</span><span class="EquationConstantsSubscript">c</span> and <span class="EquationVariables">c</span><span class="EquationConstantsSubscript">c</span> are the equivalent fluid descriptions of the porous model. This impedance model applies to any type of porous model which can be written as an equivalent fluid model. All porous models implemented in COMSOL are available for this impedance boundary condition. See details of the poroacoustics equivalent fluid models in the section <span class="Hyperlink"><a href="aco_ug_pressure.05.167.html#1128651" title="Theory for the Equivalent Fluid Models">Theory for the Equivalent Fluid Models</a></span>.</div>
  </body>
</html>