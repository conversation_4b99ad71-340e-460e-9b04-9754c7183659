<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Velocity</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="860345">Velocity</a></div>
    <div class="Body_text"><a name="819776">Use the </a><span class="Menu-Bodytext">Velocity </span>node to define the prescribed velocities <span class="EquationBold">u</span><span class="EquationConstantsSubscript">0</span> on the boundary. Add this node from the <span class="Menu-Bodytext">Mechanical </span>submenu. The condition reads <span class="EquationBold">u</span><span class="EquationConstantsSubscript">t</span><span class="EquationBold"> </span><span class="EquationVariables">= </span><span class="EquationBold">u</span><span class="EquationConstantsSubscript">0</span>. This condition is useful, for example, when modeling a vibrating wall.</div>
    <div class="Head3"><a name="819112">Velocity</a></div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="835532"><img class="Default" src="images/aco_ug_thermo.09.20.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /><img class="Default" src="images/aco_ug_thermo.09.20.2.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 292.877149606299pt">
          <div class="Body_text-middle-cell"><a name="835534">To define a prescribed velocity for each space direction (</a><span class="EquationVariables">x </span>and <span class="EquationVariables">y</span>, plus <span class="EquationVariables">z</span> for 3D), select one or more of the <span class="Menu-Bodytext">Prescribed in x direction</span>, <span class="Menu-Bodytext">Prescribed in y direction</span>, and <span class="Menu-Bodytext">Prescribed in z direction</span> check boxes. Then enter a value or expression for the prescribed velocities <span class="EquationVariables">u</span><span class="EquationConstantsSubscript">0x</span>, <span class="EquationVariables">u</span><span class="EquationConstantsSubscript">0y</span>, or <span class="EquationVariables">u</span><span class="EquationConstantsSubscript">0z</span> (SI unit: m/s).</div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="835573"><img class="Default" src="images/aco_ug_thermo.09.20.3.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 292.877149606299pt">
          <div class="Body_text-cell"><a name="835575">To define a prescribed velocity for each space direction (</a><span class="EquationVariables">r</span> and <span class="EquationVariables">z</span>), select one or both of the <span class="Menu-Bodytext">Prescribed in r direction</span> and <span class="Menu-Bodytext">Prescribed in z direction</span> check boxes. Then enter a value or expression for the prescribed velocities <span class="EquationVariables">u</span><span class="EquationConstantsSubscript">0r</span> or <span class="EquationVariables">u</span><span class="EquationConstantsSubscript">0z</span> (SI unit: m/s).</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1047288">Constraint Settings</a></div>
    <div class="Body_text"><a name="1047292">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.20.4.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. For the <span class="Menu-Bodytext">Velocity</span> condition the <span class="Menu-Bodytext">Constraint method</span> is set to <span class="Menu-Bodytext">Nodal</span>.</div>
    <div class="Head3"><a name="1047293">Excluded Edges/Points</a></div>
    <div class="Body_text"><a name="944518">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.20.5.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. See <span class="Hyperlink"><a href="aco_ug_thermo.09.45.html#1039488" title="Suppressing Constraints on Lower Dimensions">Suppressing Constraints on Lower Dimensions</a></span> for details. </div>
  </body>
</html>