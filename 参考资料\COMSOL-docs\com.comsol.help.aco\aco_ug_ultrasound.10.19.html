<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Compute Minimum and Maximum Pressure</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1659036">Compute Minimum and Maximum Pressure</a></div>
    <div class="Body_text"><a name="1659112">The </a><span class="Menu-Bodytext">Compute Minimum and Maximum Pressure</span> subfeature can be added to the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.18.html#1643392" title="Nonlinear Pressure Acoustics, Time Explicit Model">Nonlinear Pressure Acoustics, Time Explicit Model</a></span> as either a domain or a boundary feature. The minimum and maximum value of the pressure, over time and space, will be computed on the selected entity. It will create two variables <span class="Code">nate.p_min</span> and <span class="Code">nate.p_max</span>, these can be used in postprocessing to, for example, evaluate the size of a focal zone.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1659614"><img class="Default" src="images/aco_ug_ultrasound.10.19.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.564651968504pt">
          <div class="Body_text"><a name="1659620">For an example, see the </a><span class="Body_text-ital">High-Intensity Focused Ultrasound (HIFU) Propagation Through a Tissue Phantom</span> tutorial model. The Application Library path <span class="Menu-Bodytext">Acoustics_Module/ Nonlinear_Acoustics/hifu_tissue_sample</span></div>
        </td>
      </tr>
    </table>
  </body>
</html>