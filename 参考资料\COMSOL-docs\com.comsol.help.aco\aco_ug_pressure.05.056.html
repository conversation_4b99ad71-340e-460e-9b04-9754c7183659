<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Background Pressure Field (for Transient Models)</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1595225">Background Pressure Field (for Transient Models)</a></div>
    <div class="Body_text"><a name="1595227">Add a </a><span class="Menu-Bodytext">Background Pressure Field</span> node to model an background/incident pressure wave to study the scattered pressure field <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">s</span>, which is defined as the difference between the total acoustic pressure <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t </span>and the background pressure field <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span>:</div>
    <div class="Eqn"><a name="1595231"><img class="Default" src="images/aco_ug_pressure.05.056.1.png" width="82" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1595571">This feature sets up the equations in a so-called scattered field formulation where the dependent variable is the scattered field </a><span class="EquationVariables">p</span> = <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">s</span>. In a model where the background pressure field is not defined on all acoustic domains (or it is different), continuity is automatically applied in the total field <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span> on interior boundaries between domains. </div>
    <div class="Head3"><a name="1595572">Background Pressure Field</a></div>
    <div class="Body_text"><a name="1595615">Select the </a><span class="Menu-Bodytext">Pressure field type</span> as <span class="Menu-Bodytext">Plane wave (monochromatic)</span> (the default), <span class="Menu-Bodytext">Modulated Gaussian pulse</span>, or <span class="Menu-Bodytext">User defined</span>. </div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1595886">For </a><span class="Menu-Bodytext">Plane wave (monochromatic)</span> enter a value for the <span class="Menu-Bodytext">Pressure amplitude</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span> of the wave, select the <span class="Menu-Bodytext">Speed of sound</span> as <span class="Menu-Bodytext">User defined</span> (enter a value for <span class="EquationVariables">c</span>) or <span class="Menu-Bodytext">From material</span>, enter a <span class="Menu-Bodytext">Wave direction</span> vector <span class="EquationBold">e</span><span class="EquationConstantsSubscript">k</span>, enter the <span class="Menu-Bodytext">Signal frequency</span> <span class="EquationVariables">f</span><span class="EquationConstantsSubscript">0</span> of the wave, and a possible <span class="Menu-Bodytext">Phase</span> <img class="Default" src="images/aco_ug_pressure.05.056.2.png" width="13" height="20" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-textcontinued"><a name="1596472">These settings defined a background pressure field </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span> as a plane propagating wave of the type:</div>
    <div class="Eqn"><a name="1713747"><img class="Default" src="images/aco_ug_pressure.05.056.3.png" width="242" height="44" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1714446">For </a><span class="Menu-Bodytext">Modulated Gaussian pulse</span> enter a value for the <span class="Menu-Bodytext">Pressure amplitude</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span> of the wave, select the <span class="Menu-Bodytext">Speed of sound</span> as <span class="Menu-Bodytext">User defined</span> (enter a value for <span class="EquationVariables">c</span>) or <span class="Menu-Bodytext">From material</span>, enter a <span class="Menu-Bodytext">Wave direction</span> vector <span class="EquationBold">e</span><span class="EquationConstantsSubscript">k </span>and enter the <span class="Menu-Bodytext">Signal emitting plane coordinate</span> <span class="EquationBold">x</span><span class="EquationConstantsSubscript">0</span>. The latter two options define the initial location of the pulse and the propagation direction. When modeling scattering problems, the initial location should be placed outside the computational domain. Next define the frequency content of the Gaussian pulse. Select the <span class="Menu-Bodytext">Gaussian pulse bandwidth</span> as <span class="Menu-Bodytext">Full width at half maximum, power</span> (the default), <span class="Menu-Bodytext">Full width at half maximum, amplitude</span>, <span class="Menu-Bodytext">Full duration at half maximum, power</span>, <span class="Menu-Bodytext">Full duration at half maximum, amplitude</span>, or <span class="Menu-Bodytext">Standard deviation</span>. For the first two options enter a frequency bandwidth <span class="Symbol">Δ</span><span class="EquationVariables">f</span> and for the latter a time window <span class="Symbol">Δ</span><span class="EquationVariables">t</span>. Finally, enter the carrier <span class="Menu-Bodytext">Signal frequency (for time dependent studies)</span> <span class="EquationVariables">f</span><span class="EquationConstantsSubscript">0</span>.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-textcontinued"><a name="1715436">These settings defined a background pressure field </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span> as a propagating modulated Gaussian pulse of the type:</div>
    <div class="Eqn"><a name="1715495"><img class="Default" src="images/aco_ug_pressure.05.056.4.png" width="264" height="44" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Bullets-textcontinued"><a name="1714548">where the standard deviation </a><span class="Symbol">σ</span> of the Gaussian <span class="EquationVariables">G</span>(<span class="EquationBold">x</span>,<span class="EquationVariables">t</span>) depends on the bandwidth option selected. The modulated Gaussian pulse option only gives contributions when a time dependent model is solved. It has the advantage of having a defined frequency content and can be used for limited bandwidth simulations. In combination with the <span class="Hyperlink"><a href="aco_ug_pressure.05.062.html#1695649" title="Exterior Field Calculation (for Transient Models)">Exterior Field Calculation (for Transient Models)</a></span> and an FFT study, the broadband scattering characteristics of an object can be analyzed.</div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1595894">For </a><span class="Menu-Bodytext">User defined</span> enter an analytical expression that can be a function of both space and time or use a solution from a previous study.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Head3"><a name="1596764">Advanced Settings</a></div>
    <div class="Body_text"><a name="1596787">To display this section, when the </a><span class="Menu-Bodytext">Plane wave (monochromatic)</span> option is selected, click the <span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_pressure.05.056.5.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box<span class="Menu-Bodytext">.</span></div>
    <div class="Body_text"><a name="1597134">Click to select the </a><span class="Menu-Bodytext">Use ramp function</span> option (selected per default). With this option turned on, a ramp is automatically added to smoothly increase the background pressure field amplitude over the first period <span class="EquationVariables">T</span> = 1/<span class="EquationVariables">f</span><span class="EquationConstantsSubscript">0</span>. This option ensures numerical stability.</div>
  </body>
</html>