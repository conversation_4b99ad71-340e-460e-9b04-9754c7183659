<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Domain Sources</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1604526">Domain Sources</a></div>
    <div class="Body_text"><a name="1604552">Use a </a><span class="Menu-Bodytext">Domain Sources</span> node to add various sources to a domain. The feature adds right-hand side sources to either the continuity or the momentum equation.</div>
    <div class="Body_text"><a name="1608803">The </a><span class="Menu-Bodytext">Domain mass source</span> <span class="EquationVariables">q</span><span class="EquationConstantsSubscript">source </span>and the <span class="Menu-Bodytext">Domain pressure source</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">source </span>both contribute to the source term <span class="EquationVariables">f</span><span class="EquationConstantsSubscript">p</span> of the continuity equation. This is a monopole like source that acts uniformly in all directions. The two represent different formulations of the same source type.</div>
    <div class="Eqn"><a name="1608945"><img class="Default" src="images/aco_ug_ultrasound.10.05.1.png" width="153" height="50" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1608806">The </a><span class="Menu-Bodytext">Domain velocity source</span> <span class="EquationBold">u</span><span class="EquationConstantsSubscript">source </span>and <span class="Menu-Bodytext">Domain force source</span> <span class="EquationBold">f</span><span class="EquationConstantsSubscript">source </span>both contribute to the source term <span class="EquationBold">f</span><span class="EquationConstantsSubscript">v</span> of the momentum equation. This is a dipole like source that acts in the direction of its vector. The two represent different formulations of the same source type.</div>
    <div class="Eqn"><a name="1608959"><img class="Default" src="images/aco_ug_ultrasound.10.05.2.png" width="153" height="40" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1609202">All source terms can be any analytical or interpolation function of the time. The variable </a><span class="Code">t</span> is reserved to represent time in expressions.</div>
    <div class="Head3"><a name="1604673">Domain Sources</a></div>
    <div class="Body_text"><a name="1604693">In this section, add one or more of the following sources:</a></div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1604754">Add a </a><span class="Menu-Bodytext">Domain mass source</span> <span class="EquationVariables">q</span><span class="EquationConstantsSubscript">source</span> (SI unit: kg/(m<span class="Superscript-Bodytext">3</span>·s))</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1604826">Add a </a><span class="Menu-Bodytext">Domain pressure source</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">source</span> (SI unit: Pa)</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1604883">Add a </a><span class="Menu-Bodytext">Domain velocity source</span> <span class="EquationBold">u</span><span class="EquationConstantsSubscript">source</span> (SI unit: m/s).</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1604973">Add a </a><span class="Menu-Bodytext">Domain force source</span> <span class="EquationBold">f</span><span class="EquationConstantsSubscript">source</span> (SI unit: N/m<span class="Superscript-Bodytext">3</span>).</div>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>