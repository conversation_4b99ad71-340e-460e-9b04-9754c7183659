<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Solver Suggestions for Large Thermoviscous Acoustics Models</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="973409">Solver Suggestions for Large Thermoviscous Acoustics Models</a></div>
    <div class="Body_text"><a name="973410">Solving thermoviscous acoustic problems can easily involve solving for many degrees of freedom (DOFs) as the model solves for the acoustic variations in both pressure, velocity field (2 or 3 components), and temperature. First of all, it is important to restrict the use of the thermoviscous acoustics model to domains and regions of the model where it is necessary. Couple to Pressure Acoustics using the multiphysics coupling. In the frequency domain use the </a><span class="Hyperlink"><a href="aco_ug_pressure.05.006.html#1270537" title="Narrow Region Acoustics">Narrow Region Acoustics</a></span> feature or the <span class="Hyperlink"><a href="aco_ug_pressure.05.030.html#1805523" title="Thermoviscous Boundary Layer Impedance">Thermoviscous Boundary Layer Impedance</a></span> feature of Pressure Acoustics to reduce the number of DOFs. Secondly, care should be taken when meshing the computational domain. If these two things have been carefully considered, the solver can be changed from its default <span class="Body_text-ital">Direct</span> setting to use an <span class="Body_text-ital">Iterative</span> solver suggestion. Depending on the model size and involved physics two options are described below.</div>
    <div class="Body_text"><a name="998308">In both cases, a good starting point for setting up a new solver configuration is to right-click the study node and select </a><span class="Menu-Bodytext">Show Default Solver</span>, then expand the <span class="Menu-Bodytext">Solver Configuration</span> tree under <span class="Menu-Bodytext">Stationary Solver </span>or <span class="Menu-Bodytext">Time-Dependent Solver</span>. Predefined iterative solver suggestions are automatically generated. Per default, a direct solver is used and two iterative solvers are suggested and disabled. To turn on one of these approaches, right-click the solver and select <span class="Menu-Bodytext">Enable</span> (or press F4).</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1071969">The first suggestion </a><span class="Body_text-ital">(GMRES with Direct Precon.)</span> uses an iterative solver with a direct preconditioner. This method is typically faster than the direct solver and uses 20% less memory.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1071983">The second suggestion </a><span class="Body_text-ital">(GMRES with DD)</span> uses an iterative solver with the domain decomposition method. This method is very robust (also for multiphysics applications) and very memory efficient, but it can be slow.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Body_text"><a name="1071990">Both suggestions are described below as well as how to set them up manually (in most cases the default suggestions should be used). In liquids where thermal effects can be neglected, the model can be solved in the </a><span class="Menu-Bodytext">Adiabatic formulation</span> and DOFs saved. Choosing different shape functions can also reduce the memory consumption, for example, switching to the default P1-P2-P2 discretization to an all linear discretization if <span class="Menu-Bodytext">Stabilization</span> is enabled.</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><span class="Hyperlink"><a href="#973415" title="Solver Suggestions for Large Thermoviscous Acoustics Models" name="1027258">Iterative Solver with Direct Preconditioner using Hybridization</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><span class="Hyperlink"><a href="#973417" title="Solver Suggestions for Large Thermoviscous Acoustics Models" name="1027504">Iterative Solver with Domain Decomposition</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><span class="Hyperlink"><a href="#973421" title="Solver Suggestions for Large Thermoviscous Acoustics Models" name="1027527">Solving in the Adiabatic Case</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><span class="Hyperlink"><a href="#1019853" title="Solver Suggestions for Large Thermoviscous Acoustics Models" name="1027264">Choosing Shape Functions</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Head3"><a name="973415">Iterative Solver with Direct Preconditioner using Hybridization</a></div>
    <div class="Body_text"><a name="973416">For large 2D problems and 3D problems, that only involve thermoviscous acoustics, using the following approach will save around 20% memory and can speed up the solution procedure by a factor 2 or 3. Under the </a><span class="Body_text-ital">Stationary Solver</span> node take the following steps: Add an <span class="Menu-Bodytext">Iterative</span> solver with the GMRES solver. As preconditioner add the <span class="Menu-Bodytext">Direct Preconditioner</span> and switch the solver to PARDISO. Expand the <span class="Menu-Bodytext">Hybridization</span> section and select <span class="Menu-Bodytext">Multi preconditioner</span> in the <span class="Menu-Bodytext">Preconditioner variables</span> list add the <span class="Body_text-ital">Pressure</span> and <span class="Body_text-ital">Velocity field</span>. Add a second direct preconditioner with the same settings but now select only the <span class="Body_text-ital">Temperature</span> as preconditioner variables. Set the <span class="Menu-Bodytext">Pivoting Perturbation</span> to 1e-13 for the pressure-velocity group. The reason for splitting the equations up in this manner is that the energy equation is only loosely coupled to the momentum and continuity equations.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1007540"><img class="Default" src="images/aco_ug_thermo.09.41.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="1007542">See the </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_solver.32.161.html#1298810" title="Direct Preconditioner">Direct Preconditioner</a></span> section in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span> for more details.</div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1013697"><img class="Default" src="images/aco_ug_thermo.09.41.2.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.564651968504pt">
          <div class="Body_text"><a name="1013699">The direct preconditioner approach is used in the model: </a><span class="Body_text-ital">Transfer Impedance of a Perforate</span><span class="Menu-Bodytext">. </span>Application Library path <span class="Menu-Bodytext">Acoustics_Module/Tutorials/transfer_impedance_perforate</span></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="973417">Iterative Solver with Domain Decomposition</a></div>
    <div class="Body_text"><a name="973418">A more advanced approach, to handle very large 3D models, is again to use the GMRES iterative solver but now with the domain decomposition preconditioner. This approach can also be used for multiphysics problems involving several physics.</a></div>
    <div class="Body_text"><a name="1007398">Start by adding an </a><span class="Menu-Bodytext">Iterative</span> solver and select GMRES as the solver. Then right-click the iterative node and select <span class="Menu-Bodytext">Domain Decomposition (Schwarz)</span>. A good starting point for this solver, is to use the default settings with only a few changes:</div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="999255">If you set </a><span class="Menu-Bodytext">Recompute and clear subdomain</span> data to <span class="Menu-Bodytext">On</span> you will get a very memory lean solver (but the solution may take longer). <span class="Menu-Bodytext">Recompute and clear subdomain data</span> is a list with the options <span class="Menu-Bodytext">Automatic</span>, <span class="Menu-Bodytext">Off</span>, and <span class="Menu-Bodytext">On</span>. If the option <span class="Menu-Bodytext">Automatic</span> is chosen, the recompute and clear mechanism is activated if there is an out-of-memory error during the domain decomposition setup phase. The setup is then repeated with recompute and clear activated (which can be costly in terms of time but is better than failure). A warning is given in this case.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="999201">In the </a><span class="Menu-Bodytext">Coarse Level</span> section change the <span class="Menu-Bodytext">Use coarse level</span> to <span class="Menu-Bodytext">Aggregation</span>.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="999206">In the direct solver under the </a><span class="Menu-Bodytext">Coarse Solver</span> and <span class="Menu-Bodytext">Domain Solvers</span> subnodes use the PARDISO solver and set the <span class="Menu-Bodytext">Pivoting perturbation</span> to 1e-13.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Body_text"><a name="973420">This type of approach should make it possible to solve large thermoviscous acoustics models, also including multiphysics interactions, using a minimum of RAM. Possibly increase the value of the </a><span class="Menu-Bodytext">Maximum number of DOFs per subdomain</span> option to use a larger amount of the RAM at your disposition.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="999479"><img class="Default" src="images/aco_ug_thermo.09.41.3.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="999488">See the </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_solver.32.163.html#766486" title="Domain Decomposition (Schwarz)">Domain Decomposition (Schwarz)</a></span> section in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span> for more details</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="973421">Solving in the Adiabatic Case</a></div>
    <div class="Body_text-middle-cell"><a name="973422">In certain cases, it is a good assumption to not include thermal conduction in the model and treat all processes as adiabatic (isentropic). This is, for example, relevant for fluids where the thermal boundary layer is much thinner than the viscous, like in water. Not solving for the temperature field </a><span class="EquationVariables">T</span> also saves some degrees of freedom (DOFs).</div>
    <div class="Body_text"><a name="1032877">This is achieved by selecting the </a><span class="Menu-Bodytext">Adiabatic formulation</span> option under the <span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#984434" title="The Thermoviscous Acoustics, Frequency Domain Interface">Thermoviscous Acoustics Equation Settings</a></span> section. When <span class="Menu-Bodytext">Adiabatic formulation </span>is selected, all temperature options and conditions are disabled in the user interface.</div>
    <div class="Head3"><a name="1019853">Choosing Shape Functions</a></div>
    <div class="Body_text"><a name="1019890">In models with a structured mesh, it can be advantageous to switch to the serendipity shape functions instead of the default Lagrange shape functions; see </a><span class="Hyperlink"><a href="aco_ug_thermo.09.42.html#1019545" title="Lagrange and Serendipity Shape Functions">Lagrange and Serendipity Shape Functions</a></span> below. In general, if a boundary layer mesh is used (to resolve the thermal and viscous boundary layers) or if a PML is used in the model, the mesh contains structured mesh regions.</div>
  </body>
</html>