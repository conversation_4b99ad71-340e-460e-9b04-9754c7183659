<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Boundary Stress</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="825450">Boundary Stress</a></div>
    <div class="Body_text"><a name="1061037">Use the </a><span class="Menu-Bodytext">Boundary Stress </span>node to define the total surface (boundary) stress through a stress tensor, a traction force, or a pressure. Add this node from the <span class="Menu-Bodytext">Mechanical </span>submenu. The stress condition reads</div>
    <div class="Eqn"><a name="1061626"><img class="Default" src="images/aco_ug_thermo.09.22.1.png" width="333" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="828819">where </a><span class="Menu-Bodytext"><img class="Default" src="images/aco_ug_thermo.09.22.2.png" width="21" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></span> is a symmetric stress tensor, the product <span class="Menu-Bodytext"><img class="Default" src="images/aco_ug_thermo.09.22.3.png" width="31" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></span> defines a traction force. The traction condition reads</div>
    <div class="Eqn"><a name="1061746"><img class="Default" src="images/aco_ug_thermo.09.22.4.png" width="327" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1061597">where </a><span class="EquationBold">F</span><span class="EquationConstantsSubscript">tr</span> is the surface traction force. The pressure condition reads</div>
    <div class="Eqn"><a name="1061842"><img class="Default" src="images/aco_ug_thermo.09.22.5.png" width="352" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1061764">where </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">bnd </span>is the pressure applied to the boundary (acting in the normal direction).</div>
    <div class="Head3"><a name="819319">Stress</a></div>
    <div class="Body_text"><a name="1061195">Select a </a><span class="Menu-Bodytext">Stress type </span>— <span class="Menu-Bodytext">Stress tensor, Traction</span> (the default), or <span class="Menu-Bodytext">Pressure</span>. For <span class="Menu-Bodytext">Stress tensor</span> enter the (symmetric) <span class="Menu-Bodytext">Stress tensor <img class="Default" src="images/aco_ug_thermo.09.22.6.png" width="21" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></span> (SI unit: N/m<span class="Superscript-Bodytext">2</span>) components, for <span class="Menu-Bodytext">Traction</span> enter the traction force <span class="EquationBold">F</span><span class="EquationConstantsSubscript">tr</span> (SI unit: N/m<span class="Superscript-Bodytext">2</span>) components, and for <span class="Menu-Bodytext">Pressure</span> enter the boundary pressure <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">bnd</span> (SI unit: Pa).</div>
  </body>
</html>