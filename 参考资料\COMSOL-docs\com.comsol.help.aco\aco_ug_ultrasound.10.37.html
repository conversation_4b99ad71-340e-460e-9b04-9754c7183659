<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Meshing, Discretization, and Solvers</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1616772">Meshing, Discretization, and Solvers</a></div>
    <div class="Body_text"><span class="Hyperlink"><a href="aco_ug_ultrasound.10.02.html#1447802" title="The Convected Wave Equation, Time Explicit Interface" name="1605217">The Convected Wave Equation, Time Explicit Interface</a></span> (CWE) is based on the discontinuous Galerkin method also known as dG-FEM or simply dG. The method is very memory efficient and is based on a time explicit formulation. This means that it is not necessary to invert a full system matrix when stepping forward in time. Inversion of this matrix is necessary in time implicit methods and is very memory consuming for large problems. Because the CWE interface is not based on the classical FEM formulation, used in most of the other acoustics interfaces, other strategies apply for meshing and discretization.</div>
    <div class="Body_text"><a name="1622726">The internal time stepping size of a time explicit method is strictly controlled by the CFL condition and thus the mesh size. Meaning that the smallest mesh elements will restrict the time steps (see </a><span class="Hyperlink"><a href="#1633183" title="Meshing, Discretization, and Solvers">Optimizing the Mesh for DG</a></span> below). It turns out that the dG formulation has a sweet spot for speed and efficiency for wave problems. This is achieved by using fourth order (quartic) shape functions (the default in the interface) and a mesh with the element size of about half the wavelength of the highest frequency component that needs to be resolved. In practice a mesh with size set to <span class="Symbol">λ</span><span class="EquationConstantsSubscript">min</span>/2 to <span class="Symbol">λ</span><span class="EquationConstantsSubscript">min</span>/1.5 can usually be used.</div>
    <div class="Body_text"><a name="1650069">The interface is supported on all mesh types and for all combinations of mesh. The internal time step, used by the solver, is automatically calculated based on mesh size (inscribed circle metric), the wave speed and background mean flow speed, and the specifics of the solver selected. By default the time explicit solver uses the Runge-Kutta 4th order the method (RK4). This method is good as long as the mesh has a uniform mesh element size. If a mesh includes a large distribution of mesh element sizes (in particular in cases with only a few small elements) it can be advantageous to switch to the Adam-Bashforth 3 (local) method (AB3loc). This method uses intermediate local time steps in the region where there are small mesh elements. It is not expected that AB3loc will outperform RK4 for problems with a relatively uniform distribution of elements. For these cases RK4 is be the best choice. </a></div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1624665"><img class="Default" src="images/aco_ug_ultrasound.10.37.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="1624670">For general information about the two methods see </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_solver.32.114.html#1079459" title="The Time-Explicit Solver Algorithms">The Time-Explicit Solver Algorithms</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual.</span></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1633183">Optimizing the Mesh for DG</a></div>
    <div class="Body_text"><a name="1633209">As mentioned above, the internal time step, used by the solver, is controlled by the smallest mesh element in the model (relative to the speed of sound in the domain). So when meshing care should be taken to avoid small elements.</a></div>
    <div class="Body_text"><a name="1641673">To visualize a metric for the time step used in elements, plot the variable </a><span class="Code">cwe.wtc</span>. The variable is known as the <span class="Body_text-ital">cell wave time scale</span> and is proportional to the solver time step. The actual time step depends on the solver chosen. The global minimal value exists as the variable <span class="Code">cwe.wtc_min</span>. When plotting this variable set the <span class="Menu-Bodytext">Resolution</span> to <span class="Menu-Bodytext">No refinement</span> and the <span class="Menu-Bodytext">Smoothing</span> to <span class="Menu-Bodytext">None</span>, both settings in the <span class="Menu-Bodytext">Quality</span> section of the plot. This can help identify problematic mesh element in the model. To filter out elements with the large add a <span class="Menu-Bodytext">Filter</span> subnode to the plot and add a logic expression of the type: <span class="Code">cwe.wtc&lt;1.1*cwe.wtc_min</span>.</div>
    <div class="Body_text"><a name="1641641">Some important meshing and geometry strategies apply:</a></div>
    <div class="Num_list-first_outer" style="margin-left: 2pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Num_list-first_inner" style="width: 12pt; white-space: nowrap">
              <span class="Callout">1	</span>
            </div>
          </td>
          <td width="100%">
            <div class="Num_list-first_inner"><a name="1633585">In 3D mesh, always use the </a><span class="Menu-Bodytext">Avoid too small elements</span> option in the <span class="Menu-Bodytext">Element Quality Optimization</span> settings on the <span class="Menu-Bodytext">Free Tetrahedral</span> nodes in the mesh. The <span class="Menu-Bodytext">Optimization Level</span> can be set to different degrees <span class="Menu-Bodytext">Basic</span>, <span class="Menu-Bodytext">Medium</span>, or <span class="Menu-Bodytext">High</span>. Using this option can greatly improve the mesh for dG applications and thus speed up the computation significantly. It is recommended to avoid mesh elements with short edges, since these are bad for the dG method</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Num_list-inner_outer" style="margin-left: 2pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Num_list-inner_inner" style="width: 12pt; white-space: nowrap">
              <span class="Callout">2	</span>
            </div>
          </td>
          <td width="100%">
            <div class="Num_list-inner_inner"><a name="1642024">In 2D mesh, a possible strategy is to create a mapped mesh. This will give a uniform mesh distribution. This however only works if a mapped mesh can be constructed. In general the mesh can be optimized by adding an </a><span class="Menu-Bodytext">Adapt</span> node after meshing (one for each region with a given mesh size). In the <span class="Menu-Bodytext">Adaptation</span> section set <span class="Menu-Bodytext">Solution</span> to <span class="Menu-Bodytext">None</span>, <span class="Menu-Bodytext">Type of expression</span> to <span class="Menu-Bodytext">Absolute size</span>, and then in the <span class="Menu-Bodytext">Size expression</span> enter the mesh size for the domain. Then set the <span class="Menu-Bodytext">Maximum number of refinements</span> to 0. This operation will move mesh nodes around to try to maximize the mesh size and try to remove small mesh elements.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Num_list-inner_outer" style="margin-left: 2pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Num_list-inner_inner" style="width: 12pt; white-space: nowrap">
              <span class="Callout">3	</span>
            </div>
          </td>
          <td width="100%">
            <div class="Num_list-inner_inner"><a name="1633603">Avoid small edges and surfaces in the geometry as these control the mesh. Several tools exist to remedy this by using the </a><span class="Menu-Bodytext">Virtual Operations</span> in the geometry.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Num_list-inner_outer" style="margin-left: 2pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Num_list-inner_inner" style="width: 12pt; white-space: nowrap">
              <span class="Callout">4	</span>
            </div>
          </td>
          <td width="100%">
            <div class="Num_list-inner_inner"><a name="1634993">Curved surfaces and boundaries need to be resolved adequately to ensure numerical stability. Typically using a </a><span class="Menu-Bodytext">Curvature factor</span> of 0.3 to 0.4 will work well. If a curved boundary is not adequately resolved it may result in too large internal time-steps. At the same time too small elements should of course be avoided. </div>
          </td>
        </tr>
      </table>
    </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1634315"><img class="Default" src="images/aco_ug_ultrasound.10.37.2.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="1634319">For general information about optimizing quality see </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_mesh.20.59.html#667759" title="Free Tetrahedral">Element Quality Optimization</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual.</span></div>
          <div class="Body_text"><a name="1634384">For information about virtual operations see </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_geometry.19.096.html#490546" title="Virtual Geometry and Mesh Control Operations">Virtual Geometry and Mesh Control Operations</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual.</span></div>
        </td>
      </tr>
    </table>
  </body>
</html>