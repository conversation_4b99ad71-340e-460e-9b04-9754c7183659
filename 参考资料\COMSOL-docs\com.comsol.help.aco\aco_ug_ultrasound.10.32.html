<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Material Discontinuity</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1643666">Material Discontinuity</a></div>
    <div class="Body_text"><a name="1644036">The </a><span class="Menu-Bodytext">Material Discontinuity </span>node, found under the <span class="Menu-Bodytext">Interior Conditions </span>submenu, adds an interior boundary condition to handle jumps in material properties. As the interface is based on the discontinuous Galerkin method special handling is necessary when a jump exists in the specific characteristic impedance <span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">0</span> = <span class="Symbol">ρ</span><span class="EquationVariables">c</span> of the medium.</div>
    <div class="Body_text"><a name="1644037">The condition implements continuity in total pressure and in the normal velocity over the interior boundary:</a></div>
    <div class="Eqn"><a name="1644041"><img class="Default" src="images/aco_ug_ultrasound.10.32.1.png" width="126" height="48" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1643703">Subscripts “up” and “down” in the equation refer to the up and down sides of the interior boundary, relative to the normal </a><span class="EquationBold">n</span>.</div>
  </body>
</html>