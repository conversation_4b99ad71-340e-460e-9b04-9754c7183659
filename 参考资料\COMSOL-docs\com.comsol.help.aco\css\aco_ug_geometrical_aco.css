div.<PERSON><PERSON><PERSON>
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 6pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0px;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Biblio_outer
{
  margin-bottom: 6pt;
  margin-right: 2pt;
  margin-top: 0px;
}

.Biblio_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.BiblioFirst
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 6.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0px;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.BiblioFirst_outer
{
  margin-bottom: 6.0pt;
  margin-right: 2pt;
  margin-top: 0px;
}

.BiblioFirst_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Body_text
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 15pt;
  margin-bottom: 8pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Body_text_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Body_text_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 15pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Body_text-cell
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Body_text-cell_outer
{
  margin-bottom: 8pt;
  margin-top: 0pt;
}

.Body_text-cell_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Body_text-middle-cell
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Body_text-middle-cell_outer
{
  margin-bottom: 8pt;
  margin-top: 0pt;
}

.Body_text-middle-cell_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Bullets-first
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 18pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.Bullets-first_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Bullets-first_inner
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Bullets-first-cell
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 3.0pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: -9.92125984251969pt;
  text-transform: none;
  vertical-align: baseline;
}

.Bullets-first-cell_outer
{
  margin-bottom: 3.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.Bullets-first-cell_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Bullets-inner
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 18pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.Bullets-inner_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Bullets-inner_inner
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Bullets-inner-cell
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 3.0pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: -9.92125984251969pt;
  text-transform: none;
  vertical-align: baseline;
}

.Bullets-inner-cell_outer
{
  margin-bottom: 3.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.Bullets-inner-cell_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Bullets-last
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-left: 18pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.Bullets-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Bullets-last_inner
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Bullets-last-cell
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 6.0pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: -9.92125984251969pt;
  text-transform: none;
  vertical-align: baseline;
}

.Bullets-last-cell_outer
{
  margin-bottom: 6.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.Bullets-last-cell_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Bullets-textcontinued
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 18pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Bullets-textcontinued_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Bullets-textcontinued_inner
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Bullets-textcontinued-last
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-left: 18pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Bullets-textcontinued-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Bullets-textcontinued-last_inner
{
  color: Black;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Callout
{
  color: #000000;
  direction: ltr;
  font-family: Arial, Helvetica, Verdana;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bolder;
  margin-bottom: 3.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Callout_outer
{
  margin-bottom: 3.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.Callout_inner
{
  color: #000000;
  direction: ltr;
  font-family: Arial, Helvetica, Verdana;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bolder;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Callout-last
{
  color: #000000;
  direction: ltr;
  font-family: Arial, Helvetica, Verdana;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bolder;
  margin-bottom: 6.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Callout-last_outer
{
  margin-bottom: 6.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.Callout-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Arial, Helvetica, Verdana;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bolder;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.CellBody
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 3.0pt;
  margin-left: 1pt;
  margin-right: 1pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.CellBody_outer
{
  margin-bottom: 3.0pt;
  margin-right: 1pt;
  margin-top: 0.0pt;
}

.CellBody_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.CellBodymono
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 3.0pt;
  margin-left: 1pt;
  margin-right: 1pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.CellBodymono_outer
{
  margin-bottom: 3.0pt;
  margin-right: 1pt;
  margin-top: 0.0pt;
}

.CellBodymono_inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.CellHeading
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  margin-bottom: 0.0pt;
  margin-left: 1pt;
  margin-right: 1pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: uppercase;
  vertical-align: baseline;
}

.CellHeading_outer
{
  margin-bottom: 0.0pt;
  margin-right: 1pt;
  margin-top: 0.0pt;
}

.CellHeading_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  overflow: visible;
  text-align: left;
  text-transform: uppercase;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.ChapterNumber
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 43.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: right;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.ChapterNumber_outer
{
  margin-bottom: 43.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.ChapterNumber_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: right;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.ChapterTitle
{
  color: #333333;
  direction: ltr;
  font-family: Segoe UI, Arial, Verdana, Helvetica;
  font-size: 20pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 115%;
  margin-bottom: 12pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.ChapterTitle_outer
{
  margin-bottom: 12pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.ChapterTitle_inner
{
  color: #333333;
  direction: ltr;
  font-family: Segoe UI, Arial, Verdana, Helvetica;
  font-size: 20pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 115%;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Code-first
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 0pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Code-first_outer
{
  margin-bottom: 0pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Code-first_inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Code-inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 0pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Code-inner_outer
{
  margin-bottom: 0pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Code-inner_inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Code-last
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 8pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Code-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Code-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Code-small-first
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 8pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 0pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Code-small-first_outer
{
  margin-bottom: 0pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Code-small-first_inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 8pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Code-small-inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 8pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 0pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Code-small-inner_outer
{
  margin-bottom: 0pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Code-small-inner_inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 8pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Code-small-last
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 8pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 0pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Code-small-last_outer
{
  margin-bottom: 0pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Code-small-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Courier New, Courier, Monospace;
  font-size: 8pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Eqn
{
  color: #000000;
  direction: ltr;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 11pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 8pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Eqn_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Eqn_inner
{
  color: #000000;
  direction: ltr;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 11pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.EqnTable
{
  color: #000000;
  direction: ltr;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 11pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 6.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.EqnTable_outer
{
  margin-bottom: 6.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.EqnTable_inner
{
  color: #000000;
  direction: ltr;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 11pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Equation
{
  color: #000000;
  direction: ltr;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 11pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 8pt;
  margin-left: 16pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Equation_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Equation_inner
{
  color: #000000;
  direction: ltr;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 11pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.FigureTitle
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Arial, Verdana, Helvetica;
  font-size: 9pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  line-height: 12pt;
  margin-bottom: 11.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 2.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.FigureTitle_outer
{
  margin-bottom: 11.0pt;
  margin-right: 2pt;
  margin-top: 2.0pt;
}

.FigureTitle_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Arial, Verdana, Helvetica;
  font-size: 9pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  line-height: 12pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.FooterLeft
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 0.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.FooterLeft_outer
{
  margin-bottom: 0.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.FooterLeft_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.FooterRight
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 0.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: right;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.FooterRight_outer
{
  margin-bottom: 0.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.FooterRight_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: right;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Footnote
{
  color: #000000;
  direction: ltr;
  font-family: Verdana, Arial, Helvetica;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 12pt;
  margin-bottom: 9pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: justify;
  text-indent: 9.92125984251969pt;
  text-transform: none;
  vertical-align: baseline;
}

.Footnote_outer
{
  margin-bottom: 9pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Footnote_inner
{
  color: #000000;
  direction: ltr;
  font-family: Verdana, Arial, Helvetica;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 12pt;
  overflow: visible;
  text-align: justify;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Head1
{
  color: #333333;
  direction: ltr;
  font-family: Arial, Helvetica, Verdana;
  font-size: 16pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 10pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Head1_outer
{
  margin-bottom: 10pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.Head1_inner
{
  color: #333333;
  direction: ltr;
  font-family: Arial, Helvetica, Verdana;
  font-size: 16pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Head2
{
  color: #333333;
  direction: ltr;
  font-family: Arial, Helvetica, Verdana;
  font-size: 14pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 6pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 15pt;
  overflow: visible;
  padding-bottom: 3px;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Head2_outer
{
  margin-bottom: 6pt;
  margin-right: 2pt;
  margin-top: 15pt;
  padding-bottom: 3px;
}

.Head2_inner
{
  color: #333333;
  direction: ltr;
  font-family: Arial, Helvetica, Verdana;
  font-size: 14pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Head3
{
  color: #333333;
  direction: ltr;
  font-family: Segoe UI, Arial,Helvetica,Verdana;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 50%;
  margin-bottom: 4pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 15pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: uppercase;
  vertical-align: baseline;
  word-spacing: 50%;
}

.Head3_outer
{
  margin-bottom: 4pt;
  margin-right: 2pt;
  margin-top: 15pt;
}

.Head3_inner
{
  color: #333333;
  direction: ltr;
  font-family: Segoe UI, Arial,Helvetica,Verdana;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 50%;
  overflow: visible;
  text-align: left;
  text-transform: uppercase;
  vertical-align: baseline;
  word-spacing: 50%;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Head4
{
  color: #333333;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 11pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 6pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 9pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Head4_outer
{
  margin-bottom: 6pt;
  margin-right: 2pt;
  margin-top: 9pt;
}

.Head4_inner
{
  color: #333333;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 11pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Head5
{
  color: #333333;
  direction: ltr;
  font-family: Segoe UI, Verdana,Arial,Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  margin-bottom: 4pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 12pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Head5_outer
{
  margin-bottom: 4pt;
  margin-right: 2pt;
  margin-top: 12pt;
}

.Head5_inner
{
  color: #333333;
  direction: ltr;
  font-family: Segoe UI, Verdana,Arial,Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.HeaderLeft
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: -13.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.HeaderLeft_outer
{
  margin-bottom: -13.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.HeaderLeft_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.HeaderRight
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: -13.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: right;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.HeaderRight_outer
{
  margin-bottom: -13.0pt;
  margin-right: 2pt;
  margin-top: 0.0pt;
}

.HeaderRight_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: right;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.img-tables
{
  background-position: center center;
  border-left-width: thin;
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 1.0pt;
  margin-top: 0.0pt;
  overflow: visible;
  text-align: center;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.img-tables_outer
{
  border-left-width: thin;
  margin-bottom: 1.0pt;
  margin-top: 0.0pt;
}

.img-tables_inner
{
  background-position: center center;
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: center;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Note
{
  border-bottom-color: #666666;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-top-color: #666666;
  border-top-style: solid;
  border-top-width: 1px;
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 12pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 5pt;
  overflow: visible;
  padding-bottom: 3pt;
  padding-top: 3pt;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Note_outer
{
  border-bottom-color: #666666;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-top-color: #666666;
  border-top-style: solid;
  border-top-width: 1px;
  margin-bottom: 12pt;
  margin-right: 2pt;
  margin-top: 5pt;
  padding-bottom: 3pt;
  padding-top: 3pt;
}

.Note_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Num_list-first
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 14pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  padding-left: 0pt;
  padding-right: 0pt;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.Num_list-first_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
}

.Num_list-first_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Num_list-inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 14pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  padding-left: 0pt;
  padding-right: 0pt;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.Num_list-inner_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
}

.Num_list-inner_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Num_list-last
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-left: 14pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  padding-left: 0pt;
  padding-right: 0pt;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.Num_list-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
}

.Num_list-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Num_list-textcontinued
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 14pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  padding-left: 0pt;
  padding-right: 0pt;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Num_list-textcontinued_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
}

.Num_list-textcontinued_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.Num_list-textcontinued-last
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-left: 14pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  padding-left: 0pt;
  padding-right: 0pt;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.Num_list-textcontinued-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
}

.Num_list-textcontinued-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.ParagraphSpacing
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 8pt;
  margin-bottom: 0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.ParagraphSpacing_outer
{
  margin-bottom: 0pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.ParagraphSpacing_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 8pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubAlpha-first
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubAlpha-first_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubAlpha-first_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubAlpha-inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubAlpha-inner_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubAlpha-inner_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubAlpha-last
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -12pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubAlpha-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubAlpha-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubAlpha-textcontinued
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubAlpha-textcontinued_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubAlpha-textcontinued_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubAlpha-textcontinued-last
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubAlpha-textcontinued-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubAlpha-textcontinued-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubBullet-first
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -10pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubBullet-first_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubBullet-first_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubBullet-inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -10pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubBullet-inner_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubBullet-inner_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubBullet-last
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: -10pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubBullet-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubBullet-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubBullet-textcontinued
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 5pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubBullet-textcontinued_outer
{
  margin-bottom: 5pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubBullet-textcontinued_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.SubBullet-textcontinued-last
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  margin-bottom: 8pt;
  margin-left: 26pt;
  margin-right: 2pt;
  margin-top: 0pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: none;
  vertical-align: baseline;
}

.SubBullet-textcontinued-last_outer
{
  margin-bottom: 8pt;
  margin-right: 2pt;
  margin-top: 0pt;
}

.SubBullet-textcontinued-last_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 13pt;
  overflow: visible;
  text-align: left;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.TableFootnote
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 0.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 2.0pt;
  overflow: visible;
  text-align: justify;
  text-indent: -7.086614173228311pt;
  text-transform: none;
  vertical-align: baseline;
}

.TableFootnote_outer
{
  margin-bottom: 0.0pt;
  margin-right: 2pt;
  margin-top: 2.0pt;
}

.TableFootnote_inner
{
  color: #000000;
  direction: ltr;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: justify;
  text-transform: none;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.TableTitle
{
  color: #212121;
  direction: ltr;
  font-family: Arial, Segoe UI, Verdana, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 2pt;
  margin-left: 2pt;
  margin-right: 0pt;
  margin-top: 2pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: uppercase;
  vertical-align: baseline;
}

.TableTitle_outer
{
  margin-bottom: 2pt;
  margin-right: 0pt;
  margin-top: 2pt;
}

.TableTitle_inner
{
  color: #212121;
  direction: ltr;
  font-family: Arial, Segoe UI, Verdana, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: uppercase;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

div.TableTitle-first
{
  color: #212121;
  direction: ltr;
  font-family: Arial, Segoe UI, Verdana, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  margin-bottom: 2pt;
  margin-left: 2pt;
  margin-right: 0pt;
  margin-top: 2pt;
  overflow: visible;
  text-align: left;
  text-indent: 0pt;
  text-transform: uppercase;
  vertical-align: baseline;
}

.TableTitle-first_outer
{
  margin-bottom: 2pt;
  margin-right: 0pt;
  margin-top: 2pt;
}

.TableTitle-first_inner
{
  color: #212121;
  direction: ltr;
  font-family: Arial, Segoe UI, Verdana, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  overflow: visible;
  text-align: left;
  text-transform: uppercase;
  vertical-align: baseline;
  margin-left: 0pt;
  margin-right: 0pt;
  margin-top: 0pt;
  margin-bottom: 0pt;
  padding-left: 0pt;
  padding-right: 0pt;
  padding-top: 0pt;
  padding-bottom: 0pt;
}

span.Body_text, abbreviation.Body_text, acronym.Body_text, citation.Body_text
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Body_text-anfang, abbreviation.Body_text-anfang, acronym.Body_text-anfang, citation.Body_text-anfang
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Body_text-ital, abbreviation.Body_text-ital, acronym.Body_text-ital, citation.Body_text-ital
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Body_text-third_party, abbreviation.Body_text-third_party, acronym.Body_text-third_party, citation.Body_text-third_party
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 8pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Body_text-third_party_ital, abbreviation.Body_text-third_party_ital, acronym.Body_text-third_party_ital, citation.Body_text-third_party_ital
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 8pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Bullet, abbreviation.Bullet, acronym.Bullet, citation.Bullet
{
  color: Black;
  font-family: Segoe UI, Arial, Helvetica, Verdana;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Callout, abbreviation.Callout, acronym.Callout, citation.Callout
{
  color: Black;
  font-family: Arial, Helvetica, Verdana;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Callout-bold, abbreviation.Callout-bold, acronym.Callout-bold, citation.Callout-bold
{
  color: Black;
  font-family: Arial, Helvetica, Verdana;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  text-transform: none;
  vertical-align: baseline;
}

span.Callout-code, abbreviation.Callout-code, acronym.Callout-code, citation.Callout-code
{
  color: Black;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Callout-ital, abbreviation.Callout-ital, acronym.Callout-ital, citation.Callout-ital
{
  color: Black;
  font-family: Arial, Helvetica, Verdana;
  font-size: 9pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.CellBody, abbreviation.CellBody, acronym.CellBody, citation.CellBody
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.CellBody-third_party, abbreviation.CellBody-third_party, acronym.CellBody-third_party, citation.CellBody-third_party
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.CellBody-third_party_ital, abbreviation.CellBody-third_party_ital, acronym.CellBody-third_party_ital, citation.CellBody-third_party_ital
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 7pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 110%;
  text-transform: none;
  vertical-align: baseline;
}

span.CellBodyBold, abbreviation.CellBodyBold, acronym.CellBodyBold, citation.CellBodyBold
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  text-transform: none;
  vertical-align: baseline;
}

span.CellBodyItalic, abbreviation.CellBodyItalic, acronym.CellBodyItalic, citation.CellBodyItalic
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 110%;
  text-transform: none;
  vertical-align: baseline;
}

span.ChapterTitle-third_party, abbreviation.ChapterTitle-third_party, acronym.ChapterTitle-third_party, citation.ChapterTitle-third_party
{
  color: #333333;
  font-family: Segoe UI, Arial, Verdana, Helvetica;
  font-size: 18pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 115%;
  text-transform: none;
  vertical-align: baseline;
}

span.ChapterTitle-third_party_obl, abbreviation.ChapterTitle-third_party_obl, acronym.ChapterTitle-third_party_obl, citation.ChapterTitle-third_party_obl
{
  color: #333333;
  font-family: Segoe UI, Arial, Verdana, Helvetica;
  font-size: 16pt;
  font-style: oblique;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 115%;
  text-transform: none;
  vertical-align: baseline;
}

span.ChapterTitleSuperscript, abbreviation.ChapterTitleSuperscript, acronym.ChapterTitleSuperscript, citation.ChapterTitleSuperscript
{
  color: #333333;
  font-family: Segoe UI, Arial, Verdana, Helvetica;
  font-size: 12pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  letter-spacing: 115%;
  text-transform: none;
  vertical-align: super;
}

span.Code, abbreviation.Code, acronym.Code, citation.Code
{
  color: Black;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Code-bold, abbreviation.Code-bold, acronym.Code-bold, citation.Code-bold
{
  color: Black;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  text-transform: none;
  vertical-align: baseline;
}

span.Code-ital, abbreviation.Code-ital, acronym.Code-ital, citation.Code-ital
{
  color: Black;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Code-small, abbreviation.Code-small, acronym.Code-small, citation.Code-small
{
  color: Black;
  font-family: Courier New, Courier, Monospace;
  font-size: 8pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.EquationBold, abbreviation.EquationBold, acronym.EquationBold, citation.EquationBold
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 13pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  text-transform: none;
  vertical-align: baseline;
}

span.EquationBoldSubscript, abbreviation.EquationBoldSubscript, acronym.EquationBoldSubscript, citation.EquationBoldSubscript
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  text-transform: none;
  vertical-align: sub;
}

span.EquationBoldSuperscript, abbreviation.EquationBoldSuperscript, acronym.EquationBoldSuperscript, citation.EquationBoldSuperscript
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  text-transform: none;
  vertical-align: super;
}

span.EquationConstants, abbreviation.EquationConstants, acronym.EquationConstants, citation.EquationConstants
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 13pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.EquationConstantsSubscript, abbreviation.EquationConstantsSubscript, acronym.EquationConstantsSubscript, citation.EquationConstantsSubscript
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 6pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: sub;
}

span.EquationConstantsSuperscript, abbreviation.EquationConstantsSuperscript, acronym.EquationConstantsSuperscript, citation.EquationConstantsSuperscript
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 6pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: super;
}

span.EquationVariables, abbreviation.EquationVariables, acronym.EquationVariables, citation.EquationVariables
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 13pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.EquationVariablesSubscript, abbreviation.EquationVariablesSubscript, acronym.EquationVariablesSubscript, citation.EquationVariablesSubscript
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 7pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: sub;
}

span.EquationVariablesSuperscript, abbreviation.EquationVariablesSuperscript, acronym.EquationVariablesSuperscript, citation.EquationVariablesSuperscript
{
  color: Black;
  font-family: NewCenturySchlbk, Times New Roman, Arial;
  font-size: 7pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: super;
}

span.FooterBar, abbreviation.FooterBar, acronym.FooterBar, citation.FooterBar
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.FooterChapter, abbreviation.FooterChapter, acronym.FooterChapter, citation.FooterChapter
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.FooterPage, abbreviation.FooterPage, acronym.FooterPage, citation.FooterPage
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.FooterSection, abbreviation.FooterSection, acronym.FooterSection, citation.FooterSection
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-weight: normal;
  text-transform: uppercase;
  vertical-align: baseline;
}

span.Head3-third_party, abbreviation.Head3-third_party, acronym.Head3-third_party, citation.Head3-third_party
{
  color: #333333;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 9pt;
  font-style: normal;
  font-weight: normal;
  text-transform: uppercase;
  vertical-align: baseline;
}

span.Head3-third_party_obl, abbreviation.Head3-third_party_obl, acronym.Head3-third_party_obl, citation.Head3-third_party_obl
{
  color: #333333;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 9pt;
  font-style: oblique;
  font-weight: normal;
  text-transform: uppercase;
  vertical-align: baseline;
}

span.Head3Superscript, abbreviation.Head3Superscript, acronym.Head3Superscript, citation.Head3Superscript
{
  color: #333333;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 8pt;
  font-style: normal;
  font-weight: normal;
  text-transform: uppercase;
  vertical-align: super;
}

span.HeaderFunction, abbreviation.HeaderFunction, acronym.HeaderFunction, citation.HeaderFunction
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Hyperlink, abbreviation.Hyperlink, acronym.Hyperlink, citation.Hyperlink
{
  color: #3366CC;
  font-family: Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Hyperlinkfunc, abbreviation.Hyperlinkfunc, acronym.Hyperlinkfunc, citation.Hyperlinkfunc
{
  color: #0064b3;
  font-family: Courier New, Courier, Monospace;
  font-size: 9pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Menu-Bodytext, abbreviation.Menu-Bodytext, acronym.Menu-Bodytext, citation.Menu-Bodytext
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  text-transform: none;
  vertical-align: baseline;
}

span.NoteBody-ital, abbreviation.NoteBody-ital, acronym.NoteBody-ital, citation.NoteBody-ital
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.NoteHead, abbreviation.NoteHead, acronym.NoteHead, citation.NoteHead
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  text-transform: none;
  vertical-align: baseline;
}

span.NoteNum_list, abbreviation.NoteNum_list, acronym.NoteNum_list, citation.NoteNum_list
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Num_list, abbreviation.Num_list, acronym.Num_list, citation.Num_list
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.SmallEquationBold, abbreviation.SmallEquationBold, acronym.SmallEquationBold, citation.SmallEquationBold
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.SmallEquationConstants, abbreviation.SmallEquationConstants, acronym.SmallEquationConstants, citation.SmallEquationConstants
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Subscript-Bodytext, abbreviation.Subscript-Bodytext, acronym.Subscript-Bodytext, citation.Subscript-Bodytext
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 6pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: sub;
}

span.Subscript-Bodytext-ital, abbreviation.Subscript-Bodytext-ital, acronym.Subscript-Bodytext-ital, citation.Subscript-Bodytext-ital
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 7pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: sub;
}

span.Subscript-CellBody, abbreviation.Subscript-CellBody, acronym.Subscript-CellBody, citation.Subscript-CellBody
{
  color: Black;
  font-family: Segoe UI, Verdana,Arial, Helvetica;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: sub;
}

span.Subscript-MenuBodytext, abbreviation.Subscript-MenuBodytext, acronym.Subscript-MenuBodytext, citation.Subscript-MenuBodytext
{
  color: Black;
  font-family: Segoe UI, Arial, Helvetica, Verdana;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: sub;
}

span.Subscript-Symbol, abbreviation.Subscript-Symbol, acronym.Subscript-Symbol, citation.Subscript-Symbol
{
  color: Black;
  font-family: SWGrekc, SWGreks;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: sub;
}

span.Superscript-Bodytext, abbreviation.Superscript-Bodytext, acronym.Superscript-Bodytext, citation.Superscript-Bodytext
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 6pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: super;
}

span.Superscript-Bodytext-ital, abbreviation.Superscript-Bodytext-ital, acronym.Superscript-Bodytext-ital, citation.Superscript-Bodytext-ital
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 7pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: super;
}

span.Superscript-CellBody, abbreviation.Superscript-CellBody, acronym.Superscript-CellBody, citation.Superscript-CellBody
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: super;
}

span.Superscript-MenuBodytext, abbreviation.Superscript-MenuBodytext, acronym.Superscript-MenuBodytext, citation.Superscript-MenuBodytext
{
  color: Black;
  font-family: Segoe UI, Arial, Helvetica, Verdana;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: super;
}

span.Superscript-Symbol, abbreviation.Superscript-Symbol, acronym.Superscript-Symbol, citation.Superscript-Symbol
{
  color: Black;
  font-family: SWGrekc, SWGreks;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: super;
}

span.Symbol, abbreviation.Symbol, acronym.Symbol, citation.Symbol
{
  color: Black;
  font-family: SWGrekc, SWGreks;
  font-size: 11pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Symbol_H2, abbreviation.Symbol_H2, acronym.Symbol_H2, citation.Symbol_H2
{
  color: #333333;
  font-family: SWGrekc, SWGreks;
  font-size: 15pt;
  font-style: oblique;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Symbol_H4, abbreviation.Symbol_H4, acronym.Symbol_H4, citation.Symbol_H4
{
  color: #003366;
  font-family: SWGrekc, SWGreks;
  font-size: 12pt;
  font-style: italic;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Symbol-obl, abbreviation.Symbol-obl, acronym.Symbol-obl, citation.Symbol-obl
{
  color: Black;
  font-family: SWGrekc, SWGreks;
  font-size: 11pt;
  font-style: oblique;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Symbol-small, abbreviation.Symbol-small, acronym.Symbol-small, citation.Symbol-small
{
  color: Black;
  font-family: SWGrekc, SWGreks;
  font-size: 7pt;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  text-transform: none;
  vertical-align: baseline;
}

span.Num_list_B
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_B
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_B
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_G
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_G
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_Gra
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_Gra
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_M
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_M
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_O
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_O
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_P
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_P
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_T
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_T
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_Y
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_Y
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_B
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_G
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_Gra
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_M
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_O
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_P
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_T
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.Num_list_Y
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

span.
{
  color: Black;
  font-family: Segoe UI, Verdana, Arial, Helvetica;
  font-size: 10pt;
  font-weight: normal;
}

table.Format_A
{
  border-collapse: collapse;
  direction: ltr;
  margin-bottom: 12.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 12.0pt;
  padding-bottom: 4pt;
  padding-left: 6pt;
  padding-right: 6pt;
  padding-top: 6pt;
  text-align: left;
}

table.BasicTable
{
  border-collapse: collapse;
  direction: ltr;
  margin-bottom: 12.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 5.0pt;
  padding-bottom: 4pt;
  padding-left: 4pt;
  padding-right: 7pt;
  padding-top: 4pt;
  text-align: right;
}

table.Caution
{
  border-collapse: collapse;
  direction: ltr;
  margin-bottom: 12.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 16.0pt;
  padding-bottom: 4pt;
  padding-left: 7pt;
  padding-right: 7pt;
  padding-top: 6pt;
  text-align: left;
}

table.Image-Tables
{
  border-bottom-color: LightGrey;
  border-bottom-width: 1px;
  border-collapse: collapse;
  border-left-color: LightGrey;
  border-left-width: 1px;
  border-right-color: Transparent;
  border-right-width: 1px;
  border-top-color: LightGrey;
  border-top-width: 1px;
  direction: ltr;
  margin-bottom: 5pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 5pt;
  overflow: visible;
  padding-bottom: 4pt;
  padding-left: 7pt;
  padding-right: 7pt;
  padding-top: 6pt;
  text-align: left;
  width: 100%;
}

table.Basic-NoTitle
{
  background-color: White;
  border-bottom-color: Gray;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-collapse: collapse;
  border-left-color: Gray;
  border-left-style: solid;
  border-left-width: 1px;
  border-right-color: Gray;
  border-right-style: solid;
  border-right-width: 1px;
  border-top-color: Gray;
  border-top-style: solid;
  border-top-width: 1px;
  direction: ltr;
  margin-bottom: 12pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 12pt;
  overflow: visible;
  padding-bottom: 2px;
  padding-left: 2pt;
  padding-right: 7pt;
  padding-top: 2px;
  table-layout: fixed;
  text-align: left;
}

table.Basic
{
  background-color: White;
  border-bottom-color: Gray;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-collapse: collapse;
  border-left-color: Gray;
  border-left-style: solid;
  border-left-width: 1px;
  border-right-color: Gray;
  border-right-style: solid;
  border-right-width: 1px;
  border-top-color: Gray;
  border-top-style: solid;
  border-top-width: 1px;
  direction: ltr;
  margin-bottom: 12pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 12pt;
  overflow: visible;
  padding-bottom: 2px;
  padding-left: 2pt;
  padding-right: 7pt;
  padding-top: 2px;
  table-layout: fixed;
  text-align: left;
}

table.UnstructMasterPageMaps
{
  border-collapse: collapse;
  direction: ltr;
  margin-bottom: 6.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 6.0pt;
  padding-bottom: 3pt;
  padding-left: 3pt;
  padding-right: 3pt;
  padding-top: 3pt;
  text-align: left;
}

table.Format_B
{
  border-collapse: collapse;
  direction: ltr;
  margin-bottom: 12.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 12.0pt;
  padding-bottom: 4pt;
  padding-left: 6pt;
  padding-right: 6pt;
  padding-top: 6pt;
  text-align: center;
}

table.Mapping_Table
{
  border-collapse: collapse;
  direction: ltr;
  margin-bottom: 6.0pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 6.0pt;
  padding-bottom: 2pt;
  padding-left: 2pt;
  padding-right: 2pt;
  padding-top: 2pt;
  text-align: left;
}

table.NoLines
{
  border-bottom-color: Gray;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-collapse: collapse;
  border-left-color: Gray;
  border-left-style: solid;
  border-left-width: 1px;
  border-right-color: Gray;
  border-right-style: solid;
  border-right-width: 1px;
  border-top-color: Gray;
  border-top-style: solid;
  border-top-width: 1px;
  direction: ltr;
  margin-bottom: 11pt;
  margin-left: 2pt;
  margin-right: 2pt;
  margin-top: 12pt;
  padding-bottom: 2px;
  padding-left: 7pt;
  padding-right: 7pt;
  padding-top: 2px;
  text-align: left;
}

img.Default
{
  margin-bottom: -5pt;
  margin-left: 1pt;
  margin-right: 1pt;
}

