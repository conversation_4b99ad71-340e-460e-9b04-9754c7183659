<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Port</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1042563">Port</a></div>
    <div class="Body_text"><a name="1043291">The </a><span class="Menu-Bodytext">Port</span> boundary condition is used to excite and absorb acoustic waves that enter or leave waveguide structures, like small ducts or channels, in an acoustic model. The thermoviscous port formulation ensures that the nontrivial mode shapes of the acoustic velocity and thermal fields are captured correctly.</div>
    <div class="Body_text"><a name="1051286">A given port condition supports one specific propagating mode. To provide the full acoustic description, combine several port conditions on the same boundary. Typically, only the plane wave mode is propagating in small structures where the thermoviscous representation is necessary. The port condition provides a superior nonreflecting or radiation condition for waveguides compared to a simple impedance condition or a perfectly matched layer (PML) configuration. The same port boundary condition feature should not be applied to several waveguide inlets and outlets. The port condition supports S-parameter (scattering parameter) calculation but it can also be used as a source to just excite a system.</a></div>
    <div class="Body_text"><a name="1055625">The </a><span class="Menu-Bodytext">Port</span> boundary condition exists for 3D, 2D, and 2D axisymmetric models. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1051536"><img class="Default" src="images/aco_ug_thermo.09.12.01.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Body_text-cell"><a name="1051538">Only one port should be excited at a time if the purpose is to compute S-parameters. The S-parameters are defined as </a><span class="Code">ta.S11</span>, <span class="Code">ta.S21</span>, etc. and can be used in postprocessing.</div>
          <div class="Body_text-cell"><a name="1051539">In other cases, having more than one port exciting the system might be wanted, but the S-parameter variables cannot be correctly computed. So, when several ports are excited, the S-parameter output is turned off.</a></div>
          <div class="Body_text-cell"><a name="1051789">If only two ports are added to the system and a port sweep is performed, the system transfer matrix and system impedance matrices are also computed.</a></div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="1051522">On a given boundary, a combination of ports will define the total acoustic fields (sum of incident and outgoing pressure, temperature, and velocity waves) as</a></div>
    <div class="Eqn"><a name="1051526"><img class="Default" src="images/aco_ug_thermo.09.12.02.png" width="287" height="147" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1048512">where the summation “</a><span class="EquationConstants">i</span>” is over all ports on the given boundary “<span class="Code">bnd</span>”, <span class="EquationVariables">S</span><span class="EquationConstantsSubscript">ij</span> is the scattering parameter, <span class="EquationVariables">A</span><span class="EquationConstantsSuperscript">in</span> is the amplitude of the incident field and <span class="Symbol">φ</span> the phase (at port “<span class="EquationConstants">j</span>”), and <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">i</span>, <span class="EquationBold">u</span><span class="EquationConstantsSubscript">i</span>, and <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">i</span> are the mode shape of the <span class="EquationConstants">i</span>-th port. The mode shape is normalized to have either a unit maximum amplitude (for the pressure <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">i</span>) or carry unit power (see the normalization option in the <span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#1048021" title="The Thermoviscous Acoustics, Frequency Domain Interface">Global Port Settings</a></span> section). For both definitions the scattering parameter <span class="EquationVariables">S</span><span class="EquationConstantsSubscript">ij </span>defines the amplitude of mode <span class="EquationConstants">i</span> when a system is exited at port <span class="EquationConstants">j</span> (with mode <span class="EquationConstants">j</span>). For the power scaling, |<span class="EquationVariables">S</span><span class="EquationConstantsSubscript">ij</span>|<span class="EquationConstantsSuperscript">2</span> directly gives the power of the given mode. This corresponds to a multimode expansion of the solution on the given boundary. The scattering parameters are automatically calculated when an acoustic model is set up with just one port exciting the system. To get the full scattering matrix <span class="Hyperlink"><a href="#1048532" title="Port">The Port Sweep Functionality</a></span> can be used.</div>
    <div class="Head3"><a name="1052031">Port Properties</a></div>
    <div class="Body_text"><a name="1052032">Enter a unique </a><span class="Menu-Bodytext">Port name</span>. Only nonnegative integer numbers can be used as <span class="Menu-Bodytext">Port name</span> as it is used to define the elements of the S-parameter matrix. The numeric port names are also required for the port sweep functionality. The port name is automatically incremented by one every time a port condition is added.</div>
    <div class="Body_text"><a name="1051957">Select a </a><span class="Menu-Bodytext">Type of port</span>: <span class="Menu-Bodytext">User defined </span>(the default), <span class="Menu-Bodytext">Numeric (0,0)-mode</span>, <span class="Menu-Bodytext">Circular (0,0)-mode</span>, <span class="Menu-Bodytext">Slit (0,0)-mode</span>, or <span class="Menu-Bodytext">Plane wave</span>. Depending on the selection, different options appear in the <span class="Menu-Bodytext">Port Mode Settings</span> section (see below). Use the <span class="Menu-Bodytext">Circular (0,0)-mode </span>for a port with a circular cross section in 3D or 2D axisymmetry and the <span class="Menu-Bodytext">Slit (0,0)-mode</span> option in 2D. If the port has a different cross section than either of these, use the <span class="Menu-Bodytext">User defined</span> option or the <span class="Menu-Bodytext">Numeric (0,0)-mode </span>port. The <span class="Menu-Bodytext">Plane wave</span> option represents a situation where the boundary layers are not included, this is for example for a wave propagating in free space or when slip and adiabatic (or symmetry) conditions are applied to all adjacent boundaries.</div>
    <div class="Head3"><a name="1052164">Port Mode Settings</a></div>
    <div class="Body_text"><a name="1052165">Depending on the option selected in the </a><span class="Menu-Bodytext">Type of port</span> (see above):</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1052166">For </a><span class="Menu-Bodytext">User defined</span>, enter user defined expressions for the <span class="Menu-Bodytext">Mode shape</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">n</span>, <span class="EquationBold">u</span><span class="EquationConstantsSubscript">n</span>, <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">n, </span>and the <span class="Menu-Bodytext">Mode wave number</span> <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">n</span> (SI unit: rad/m). The mode shape will automatically be scaled before it is used in the port condition. Use the user-defined option to enter a known analytical expression or to use the solution from <span class="Hyperlink"><a href="aco_ug_thermo.09.33.html#986235" title="The Thermoviscous Acoustics, Boundary Mode Interface">The Thermoviscous Acoustics, Boundary Mode Interface</a></span>. The solutions from the boundary mode analysis can be referenced using the <span class="Code">withsol()</span> operator.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1052324">The </a><span class="Menu-Bodytext">Numeric (0,0)-mode </span>port options is used for waveguides of arbitrary cross sections. In this case, the shape of the propagating plane-wave mode (0,0) is solved on the port face. The boundary conditions for the mode are taken from the adjacent waveguide boundaries. This automatic detection works for slip, no-slip, adiabatic, isothermal, and symmetry conditions (including the same options when selected in the wall condition). If all the adjacent wave guide boundaries are slip and adiabatic (or symmetry) then use the <span class="Menu-Bodytext">Plane wave</span> option.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-textcontinued"><a name="1063111">For this option, a special solver sequence is automatically generated since the port mode shape (the port variables </a><span class="Code">psi</span>, <span class="Code">Psi_th</span>, <span class="Code">Psi_v</span>, and <span class="Code">vip</span>) should be solved before the domain problem (the main degrees of freedom <span class="Code">p</span>, <span class="Code">u</span>, <span class="Code">T</span>, and <span class="Code">Sparam1</span>). If an iterative solver suggestion is to be used, keep the linear solver for the first segregated step and then select the iterative suggestion for the second segregated step.</div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1063866">The </a><span class="Menu-Bodytext">Circular (0,0)-mode </span>port option is used for waveguides of circular cross section. The analytical mode is a plane-wave mode (0,0) given by a constant cross section pressure, no-slip condition for the velocity, and isothermal condition for the temperature. An example of the propagating mode shape in a cylindrical waveguide is seen below. <img class="Default" src="images/aco_ug_thermo.09.12.03.png" width="511" height="148" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="FigureTitle">Figure 6-1: <a name="1052436">Plane wave mode for a circular duct of 1 mm diameter at f = 250 Hz.</a></div>
    <div class="Bullets-textcontinued"><a name="1063491">Select how the </a><span class="Menu-Bodytext">Circle radius</span> of the cross section is defined, either <span class="Menu-Bodytext">Automatic</span> (the default) or <span class="Menu-Bodytext">User defined</span>. The latter option can for some geometry configurations increase the numerical precision of the computed mode.</div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1052998">The </a><span class="Menu-Bodytext">Slit (0,0)-mode </span>port option only exists in 2D on a boundary. In 2D, the geometry is assumed infinite in the out-of-plane direction and represents a slit. The analytical mode is a plane-wave mode (0,0) given by a constant cross section pressure, no-slip condition for the velocity, and isothermal condition for the temperature.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1078284">The </a><span class="Menu-Bodytext">Plane wave</span> port option represents a situation where the boundary layers are not included in the mode shape, this is, for example, for a wave propagating in free space or when slip and adiabatic (or symmetry) conditions are applied to all adjacent boundaries. </div>
          </td>
        </tr>
      </table>
    </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1053172"><img class="Default" src="images/aco_ug_thermo.09.12.04.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Body_text-cell"><a name="1053174">The solution for the numeric port is obtained using the generalized low reduced frequency (LRF) approximation commonly used in thermoviscous acoustics. The theory is based on an order of magnitude analysis of the full thermoviscous equations and on the assumption of a plane propagating mode (constant pressure in cross section). The circular and slit options use an analytical solution for the given cross section.</a></div>
          <div class="Body_text-cell"><a name="1053555">The mode shapes based on LRF approximation are valid as long as the wavelength is much larger than the waveguide cross section </a><span class="Symbol">(λ </span>&gt;&gt; <span class="EquationVariables">a</span>) and the wavelength is much larger than the boundary layer thickness <span class="Symbol">(λ </span>&gt;&gt; <span class="Symbol">δ</span><span class="EquationConstantsSubscript">visc</span> and <span class="Symbol">λ </span>&gt;&gt; <span class="Symbol">δ</span><span class="EquationConstantsSubscript">therm</span>).</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1052277">Incident Mode Settings</a></div>
    <div class="Body_text"><a name="1054975">Activate if the given port is excited by an incident wave of the given mode shape. For the first </a><span class="Menu-Bodytext">Port</span> condition added in a model, the <span class="Menu-Bodytext">Incident wave excitation at this port</span> is set to <span class="Menu-Bodytext">On</span>. For subsequent conditions added the excitation is set to <span class="Menu-Bodytext">Off</span> per default. If more than one port in a model is excited, the S-parameter calculation is not performed.</div>
    <div class="Body_text"><a name="1054976">When the </a><span class="Menu-Bodytext">Incident wave excitation at this port</span> is set to <span class="Menu-Bodytext">On</span>, then select how to define the incident wave. Select the <span class="Menu-Bodytext">Define incident wave</span>: <span class="Menu-Bodytext">Amplitude</span> (the default) or <span class="Menu-Bodytext">Power</span> (<span class="Menu-Bodytext">Power per unit length</span> in 2D models)</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1054977">For </a><span class="Menu-Bodytext">Amplitude</span> enter the amplitude <span class="EquationVariables">A</span><span class="EquationConstantsSuperscript">in</span> (SI unit: Pa) of the incident wave. This is in general defined as the maximum pressure amplitude for a given mode shape.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1063923">For </a><span class="Menu-Bodytext">Power</span> enter the power <span class="EquationVariables">P</span><span class="EquationConstantsSuperscript">in</span> (SI unit: W) of the incident mode. In 2D models this will be a <span class="Menu-Bodytext">Power per unit length</span> (SI unit: W/m).</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1054979">Enter the phase </a><span class="Symbol">φ</span> (SI unit: rad) of the incident wave. This phase contribution is multiplied with the amplitude defined through the above two options. The <span class="Menu-Bodytext">Amplitude</span> input can be a complex number.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Body_text"><a name="1052311">Note, that when the </a><span class="Menu-Bodytext">Activate port sweep</span> option is selected at the physics level, the options in the <span class="Menu-Bodytext">Incident Mode Settings</span> section are deactivated. This is because this option automatically sends in a mode of unit amplitude, sweeping through one port at the time.  </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1054986"><img class="Default" src="images/aco_ug_thermo.09.12.05.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Body_text-cell"><a name="1054988">All ports with incident wave excitation turned off have an arrow symbol, added in the Graphics window, that points outward. When incident wave excitation is turned on, the arrow symbol points inward.</a></div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1054994"><img class="Default" src="images/aco_ug_thermo.09.12.06.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Body_text-cell"><a name="1054996">If a waveguide is cut with one or several symmetry planes, care should be taken when setting up the port modes and when postprocessing.</a></div>
          <div class="Body_text-cell"><a name="1054997">For the </a><span class="Menu-Bodytext">Circular</span> and <span class="Menu-Bodytext">Slit</span> options, make sure to only select modes that are actually symmetric according to the symmetry planes.</div>
          <div class="Body_text-cell"><a name="1054998">When postprocessing, remember that absolute values like, for example, the outgoing power at port 1, </a><span class="Code">ta.port1.P_out</span>, need to be multiplied with an appropriate factor. Multiplication with two if one symmetry plane is used, for example.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1047053">Constraint Settings</a></div>
    <div class="Body_text"><a name="1047057">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.12.07.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box.</div>
    <div class="Head3"><a name="1047058">Excluded Edges/Points</a></div>
    <div class="Body_text"><a name="1047047">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.12.08.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. See <span class="Hyperlink"><a href="aco_ug_thermo.09.45.html#1039488" title="Suppressing Constraints on Lower Dimensions">Suppressing Constraints on Lower Dimensions</a></span> for details</div>
    <div class="Head3"><a name="1048532">The Port Sweep Functionality</a></div>
    <div class="Body_text"><a name="1048634">The port sweep functionality is used to reconstruct the full scattering matrix </a><span class="EquationVariables">S</span><span class="EquationConstantsSubscript">ij </span>by automatically sweeping the port excitation through all the ports included in the model. When the port sweep is activated, the options in the <span class="Menu-Bodytext">Incident Mode Settings</span> in the port conditions are deactivated and COMSOL controls which port is excited with an incident mode.</div>
    <div class="Body_text"><a name="1048571">The port sweep functionality is activated at the main physics interface level by selecting </a><span class="Menu-Bodytext">Activate port sweep </span>in the <span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#1048021" title="The Thermoviscous Acoustics, Frequency Domain Interface">Global Port Settings</a></span> section. Enter the <span class="Menu-Bodytext">Sweep parameter name</span>, the default is <span class="Code">PortName</span>. Create a parameter with the same name under <span class="Menu-Bodytext">Global Definitions&gt;Parameters 1</span>. This is the name of the parameter to be used in a parametric sweep; it should represent the <span class="Menu-Bodytext">Port name</span> integer values (defined when adding the port conditions). Add a parametric sweep study step and run the sweep over the <span class="Code">PortName</span> parameter with an integer number of values representing all the ports in the model. Once the model is solved, the full scattering matrix can be evaluated using the defined global variables <span class="Code">ta.S11</span>, <span class="Code">ta.S21</span>, <span class="Code">ta.S12</span>, and so on. The transmission loss (TL) between two given ports is also computed, for example, the variable for the TL loss from port 1 to 2 is given by <span class="Code">ta.TL_12</span>. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1048644"><img class="Default" src="images/aco_ug_thermo.09.12.09.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Body_text-cell"><a name="1048646">Use the </a><span class="Menu-Bodytext">Global Matrix Evaluation</span> under <span class="Menu-Bodytext">Derived Values</span> to evaluate the full scattering matrix <span class="Code">ta.S</span>.</div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="1050056">If only two ports are added to the thermoviscous model, COMSOL also automatically computes the transfer matrix of the system (variables </a><span class="Code">ta.T11</span>, <span class="Code">ta.T12</span>, <span class="Code">ta.T21</span>, <span class="Code">ta.T22</span>) and the impedance matrix of the system (<span class="Code">ta.Z11</span>, <span class="Code">ta.Z12</span>, <span class="Code">ta.Z21</span>, <span class="Code">ta.Z22</span>). These expressions are only true if plane wave modes are used. This is nearly the case in all configurations when working with microacoustic systems. For ports in thermoviscous acoustics, the <span class="Menu-Bodytext">Numeric (0,0)-mode</span>, <span class="Menu-Bodytext">Circular (0,0)-mode</span>, <span class="Menu-Bodytext">Slit (0,0)-mode</span>, and <span class="Menu-Bodytext">Plane wave</span> options are for plane waves only, that is, the (0,0) mode with varying boundary conditions. Higher-order modes can only be introduced with the <span class="Menu-Bodytext">User defined</span> option. The transfer matrix representation is often used in electroacoustic modeling.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1050994"><img class="Default" src="images/aco_ug_thermo.09.12.10.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.564651968504pt">
          <div class="Body_text"><span class="Body_text-ital"><a name="1050999">Wax Guard Acoustics: Transfer Matrix Computation</a></span>. The Application Library path: <span class="Menu-Bodytext">Acoustics_Module/Tutorials,_Thermoviscous_Acoustics/wax_guard_acoustics</span></div>
          <div class="Body_text"><span class="Body_text-ital"><a name="1078170">Transfer Impedance of a Perforate</a></span>. The Application Library path: <span class="Menu-Bodytext">Acoustics_Module/Tutorials,_Thermoviscous_Acoustics/transfer_impedance_perforate</span></div>
        </td>
      </tr>
    </table>
  </body>
</html>