<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Background Acoustic Fields</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1021330">Background Acoustic Fields</a></div>
    <div class="Body_text"><a name="1021755">When the </a><span class="Menu-Bodytext">Background Acoustic Fields</span> feature is added to a domain, it is possible to define the value of the background acoustic field variables <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span>, <span class="EquationBold">u</span><span class="EquationConstantsSubscript">b</span>, and <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">b</span>. Using this feature, it is possible to set up scattering problems as well as defining acoustic fields at an inlet of a waveguide (using a small domain at the inlet). The feature cannot be used together with the <span class="Hyperlink"><a href="aco_ug_thermo.09.31.html#1058860" title="Nonlinear Thermoviscous Acoustics Contributions">Nonlinear Thermoviscous Acoustics Contributions</a></span>, as splitting into background and scattered field is not possible for nonlinear equations.</div>
    <div class="Head3"><a name="1021758">Background Acoustic Fields</a></div>
    <div class="Body_text"><a name="1021657">Enter expressions for the </a><span class="Menu-Bodytext">Background acoustic pressure</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span>, the <span class="Menu-Bodytext">Background acoustic velocity</span> <span class="EquationBold">u</span><span class="EquationConstantsSubscript">b</span>, and the <span class="Menu-Bodytext">Background temperature variation</span> <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">b</span>. These can be analytical expressions or values of dependent variables solved in a previous study that defines the background field. The time-domain version of the background acoustics field feature does not include the plane wave option available for the frequency domain interface.</div>
  </body>
</html>