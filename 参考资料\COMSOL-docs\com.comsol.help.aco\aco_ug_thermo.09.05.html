<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Background Acoustic Fields</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="988838">Background Acoustic Fields</a></div>
    <div class="Body_text"><a name="988873">When the </a><span class="Menu-Bodytext">Background Acoustic Fields</span> feature is added to a domain, it is possible to define the value of the background acoustic field variables <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span>, <span class="EquationBold">u</span><span class="EquationConstantsSubscript">b</span>, and <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">b</span>. Use this feature to set up scattering problems. To define fields at the inlet of a waveguide, it is recommended to use the <span class="Hyperlink"><a href="aco_ug_thermo.09.12.html#1042563" title="Port">Port</a></span> condition.</div>
    <div class="Head3"><a name="990913">Model Inputs</a></div>
    <div class="Body_text"><a name="990836">This section contains field variables that appear as model inputs. The fields are always active as the equilibrium (background) temperature </a><span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span> enters the governing energy equation explicitly. It is used for the <span class="Menu-Bodytext">Plane wave</span> option.</div>
    <div class="Head3"><a name="990929">Background Acoustic Fields</a></div>
    <div class="Body_text"><a name="991020">Select the </a><span class="Menu-Bodytext">Acoustic field type</span> — <span class="Menu-Bodytext">User defined </span>(the default) or <span class="Menu-Bodytext">Plane wave</span>.</div>
    <div class="Body_text"><a name="991371">When </a><span class="Menu-Bodytext">User defined</span> is selected, enter expressions for the <span class="Menu-Bodytext">Background acoustic pressure</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span>, the <span class="Menu-Bodytext">Background acoustic velocity</span> <span class="EquationBold">u</span><span class="EquationConstantsSubscript">b</span>, and the <span class="Menu-Bodytext">Background temperature variation</span> <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">b</span>. These can be analytical expressions or values of dependent variables solved in a previous study that defines the background field.</div>
    <div class="Body_text"><a name="991543">When </a><span class="Menu-Bodytext">Plane wave</span> is selected, enter the <span class="Menu-Bodytext">Pressure Amplitude</span> |<span class="EquationVariables">p</span><span class="EquationConstantsSubscript">b</span>| (given at <span class="EquationBold">x</span> = <span class="EquationBold">0</span>) and the wave direction vector <span class="EquationBold">e</span><span class="EquationConstantsSubscript">k</span> (this vector is automatically normalized). Finally, select where the <span class="Menu-Bodytext">Material data</span> should be taken from; the default is <span class="Menu-Bodytext">From material model </span>(the same as the domain model material) or select a material from the list. This will set up the background pressure, velocity, and temperature variations for a plane wave with the correct thermoviscous attenuation compatible with the governing equations. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1083396"><img class="Default" src="images/aco_ug_thermo.09.05.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 292.314642519685pt">
          <div class="Body_text"><a name="1083401">For many applications the </a><span class="Menu-Bodytext">Plane wave</span> option available in the <span class="Hyperlink"><a href="aco_ug_thermo.09.12.html#1042563" title="Port">Port</a></span> condition is a good alternative to the <span class="Menu-Bodytext">Background Acoustic Fields </span>feature. The port automatically ensures a non-reflective behavior for the reflected outgoing waves.</div>
        </td>
      </tr>
    </table>
  </body>
</html>