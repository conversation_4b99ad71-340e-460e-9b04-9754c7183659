<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Normal Velocity</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1597802">Normal Velocity</a></div>
    <div class="Body_text"><a name="1597804">The </a><span class="Menu-Bodytext">Normal Velocity</span> adds an inward normal velocity either given as a scalar <span class="EquationVariables">v</span><span class="EquationConstantsSubscript">n</span> or as a velocity vector <span class="EquationBold">v</span><span class="EquationConstantsSubscript">b</span>, the condition given is</div>
    <div class="Eqn"><a name="1597808"><img class="Default" src="images/aco_ug_ultrasound.10.08.1.png" width="177" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1597814">where </a><span class="EquationBold">n</span> is the surface normal. Both expressions should be functions of time. This feature represents an external source term. It can also be used to model a vibrating transducer surface or other vibrating surfaces.</div>
    <div class="Head3"><a name="1597815">Normal velocity</a></div>
    <div class="Body_text"><a name="1597816">Select a </a><span class="Menu-Bodytext">Type</span> — <span class="Menu-Bodytext">Inward Velocity</span> (the default) or <span class="Menu-Bodytext">Velocity</span>. </div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1597817">For </a><span class="Menu-Bodytext">Inward Velocity </span>enter an expression for the <span class="Menu-Bodytext">Inward velocity</span> <span class="EquationVariables">v</span><span class="EquationConstantsSubscript">n</span>(<span class="EquationVariables">t</span>) (SI unit: m/s). Use a positive value for inward velocity or a negative value for outward velocity. </div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1597818">For </a><span class="Menu-Bodytext">Velocity </span>enter expressions for the components of the <span class="Menu-Bodytext">Velocity</span> <span class="EquationBold">v</span><span class="EquationConstantsSubscript">b</span>(<span class="EquationVariables">t</span>)<span class="EquationVariablesSubscript"> </span>(SI unit: m/s).</div>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>