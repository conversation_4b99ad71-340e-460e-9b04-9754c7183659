<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Thermoviscous Acoustics Model</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1021299">Thermoviscous Acoustics Model</a></div>
    <div class="Body_text"><a name="1022194">Use the </a><span class="Menu-Bodytext">Thermoviscous Acoustics Model</span> node to define the model inputs (the background equilibrium temperature and pressure) and the material properties of the fluid (equilibrium density, dynamic viscosity, bulk viscosity, thermal conductivity, and heat capacity at constant pressure) necessary to model the transient propagation of acoustic compressible waves in a thermoviscous acoustic context. Extended inputs are available for the coefficient of thermal expansion and the compressibility, which enables modeling of any constitutive relation for the fluid.</div>
    <div class="Body_text"><a name="1065046">The equations solved are the linear (first order) perturbation equations for the conservation of mass, momentum, and energy. The model also assumes a linear equation of state relating acoustic density, pressure, and temperature fluctuations. The governing equations are given by:</a></div>
    <div class="Equation"> (6-1)<a name="1075981"><img class="Default" src="images/aco_ug_thermo.09.30.1.png" width="455" height="153" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1065039">the equations are formulated in terms of the total acoustic fields </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span>, <span class="EquationBold">u</span><span class="EquationConstantsSubscript">t</span>, and <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">t</span>. If the <span class="Hyperlink"><a href="aco_ug_thermo.09.32.html#1021330" title="Background Acoustic Fields">Background Acoustic Fields</a></span> feature is added, for modeling scattering problems or as a source, the total fields are the sum of the background acoustic fields and the scattered acoustic fields. The equations can be extended to include nonlinear effects by adding the <span class="Hyperlink"><a href="aco_ug_thermo.09.31.html#1058860" title="Nonlinear Thermoviscous Acoustics Contributions">Nonlinear Thermoviscous Acoustics Contributions</a></span>.</div>
    <div class="Body_text"><a name="1022195">The </a><span class="Menu-Bodytext">Model Inputs</span>, the <span class="Menu-Bodytext">Thermoviscous Acoustics Model</span>, and the <span class="Menu-Bodytext">Thermal Expansion and Compressibility</span> sections are the same as for the frequency domain interface. They are described under the <span class="Hyperlink"><a href="aco_ug_thermo.09.04.html#950471" title="Thermoviscous Acoustics Model">Thermoviscous Acoustics Model</a></span> section in <span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#954726" title="The Thermoviscous Acoustics, Frequency Domain Interface">The Thermoviscous Acoustics, Frequency Domain Interface</a></span>.</div>
    <div class="Body_text"><a name="1021651">Note that for the transient interface, it is equally important as for the frequency domain interface, to set the material parameters correctly. The values of the coefficient of thermal expansion and the isothermal compressibility are prone to errors, so make sure to take a thorough look at the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.04.html#912241" title="Thermoviscous Acoustics Model">Thermal Expansion and Compressibility</a></span> section.</div>
  </body>
</html>