<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>The Nonlinear Thermoviscous Acoustics Equations</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1079918">The Nonlinear Thermoviscous Acoustics Equations</a></div>
    <div class="Body_text"><a name="1079973">The governing equations solved in a transient thermoviscous acoustics model that uses the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.31.html#1058860" title="Nonlinear Thermoviscous Acoustics Contributions">Nonlinear Thermoviscous Acoustics Contributions</a></span> feature are also derived from <span class="Hyperlink"><a href="aco_ug_thermo.09.48.html#826885" title="General Linearized Compressible Flow Equations">Equation 6-4</a></span> using a first order perturbation approach. However nonlinear terms in the perturbations are retained. This yields the set of nonlinear thermo acoustics equations</div>
    <div class="Eqn"><a name="1080462"><img class="Default" src="images/aco_ug_thermo.09.54.1.png" width="411" height="301" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1080382">In the default formulation only the linear equation of state is retained. This is a good approximation in most cases as cumulative nonlinear effects typically can be neglected in microacoustics. The nonlinear equations will capture local nonlinear effects like vortex shedding. These can occur when the linearity condition |</a><span class="EquationBold">u</span><span class="EquationConstantsSubscript">t</span>| &lt;&lt; <span class="EquationVariables">c</span> is no longer fulfilled. Note that the vortices are generated by the acoustic field itself.</div>
    <div class="Body_text"><a name="1081170">In models with high local sound pressure levels, the linear equation of state may no longer be valid. This happens when the linearity condition on the density </a><span class="Symbol">ρ</span><span class="EquationConstantsSubscript">t</span> &lt;&lt; <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span> is no longer fulfilled. If necessary, the feature has an option to change the default <span class="Menu-Bodytext">Density expansion</span> from <span class="Menu-Bodytext">First order</span> to <span class="Menu-Bodytext">Second order</span>. This corresponds to the following second order equation of state</div>
    <div class="Eqn"><a name="1081266"><img class="Default" src="images/aco_ug_thermo.09.54.2.png" width="369" height="56" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1081056">Additional inputs to the model are necessary. For the general case, the second order derivatives of the equilibrium density </a><span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span> = <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span>(<span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span>,<span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span>) with respect to pressure <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0 </span>and temperature <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span> are needed. They contribute to the second-order Taylor expansion of the density. Per default, they are taken <span class="Menu-Bodytext">From equilibrium density</span>; this implies that the dependency of the density on pressure and temperature should be correct. Note that this dependency is included if a material is defined using the functionality of the Liquid and Gas Properties Module (see the <span class="Hyperlink"><a href="aco_ug_properties_fluids.16.2.html#1253786" title="Material Properties">Material Properties</a></span> section in the <span class="Hyperlink"><a href="aco_ug_properties_fluids.16.1.html#740711" title="Acoustic Properties of Fluids">Acoustic Properties of Fluids</a></span> chapter for details).</div>
  </body>
</html>