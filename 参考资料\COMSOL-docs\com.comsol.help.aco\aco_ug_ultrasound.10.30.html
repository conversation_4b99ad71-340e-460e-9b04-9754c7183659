<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Interior Sound Hard Boundary (Wall)</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1645622">Interior Sound Hard Boundary (Wall)</a></div>
    <div class="Body_text"><a name="1645667">The </a><span class="Menu-Bodytext">Interior Sound Hard Boundary (Wall) </span>node, found under the <span class="Menu-Bodytext">Interior Conditions </span>submenu, adds the boundary condition for a sound hard boundary or wall on interior boundaries. The condition reads</div>
    <div class="Eqn"><a name="1645671"><img class="Default" src="images/aco_ug_ultrasound.10.30.1.png" width="102" height="48" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1645663">The condition ensures that the total normal velocity is zero on both the up and down side of the boundary, while applying a slit on the pressure (the pressure is discontinuous across the boundary).</a></div>
  </body>
</html>