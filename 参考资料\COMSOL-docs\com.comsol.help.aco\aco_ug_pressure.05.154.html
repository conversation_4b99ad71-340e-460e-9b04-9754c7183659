<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>The Nonlinear Westervelt Equation</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1677035">The Nonlinear Westervelt Equation</a></div>
    <div class="Body_text"><a name="1677089">To extend the validity of the small perturbation wave equation to finite amplitude waves, nonlinear effects need to be taken into account. The first idea is to expand the density pressure relation to second order to include the effect of finite amplitude waves. This gives</a></div>
    <div class="Equation"> (2-20)<a name="1722730"><img class="Default" src="images/aco_ug_pressure.05.154.1.png" width="164" height="50" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1722707">where we have kept the subscript “1” to highlight the acoustic perturbation quantities and a subscript “0” for the quiescent quantities. See </a><span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203183" title="References for the Pressure Acoustics Branch">Ref. 6</a></span> and <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1677442" title="References for the Pressure Acoustics Branch">42</a></span> for details. The quantity <span class="EquationVariables">B</span>/<span class="EquationVariables">A</span> is the parameter of nonlinearity (see <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1722331" title="References for the Pressure Acoustics Branch">Ref. 51</a></span>). This expression basically means that the different parts of the acoustic pressure wave travel at different speeds.</div>
    <div class="Body_text"><a name="1724589">Inserting the above into the governing </a><span class="Hyperlink"><a href="aco_ug_pressure.05.151.html#1174562" title="The Governing Equations">Equation 2-11</a></span> (retaining the dissipative terms) will lead to the full nonlinear second-order wave equation. This is the nonlinear equivalent to <span class="Hyperlink"><a href="aco_ug_pressure.05.151.html#1174572" title="The Governing Equations">Equation 2-12</a></span> (or actually the nonlinear equivalent to the full equations of thermoviscous acoustics). Next assume that boundary layer effects can be disregarded and cumulative nonlinear effects dominate local nonlinear effects, for example, when the propagation distance is greater than the wavelength. This leads to the well-known Westervelt equation (see <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1677442" title="References for the Pressure Acoustics Branch">Ref. 42</a></span>)</div>
    <div class="Equation"> (2-21)<a name="1722458"><img class="Default" src="images/aco_ug_pressure.05.154.2.png" width="369" height="56" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1722400">Here we have eliminated the density fluctuations and then removed the subscripts. Hence, </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span> is the (total) acoustic pressure, <span class="Symbol">ρ</span> is the quiescent density, <span class="Symbol">δ</span> is the diffusivity of sound, and <span class="Symbol">β</span> is the coefficient of nonlinearity (related to the parameter of nonlinearity <span class="EquationVariables">B</span>/<span class="EquationVariables">A</span>).</div>
    <div class="Body_text"><a name="1723953">The Westervelt equation supports the propagation of shocks. Shocks can be hard to capture numerically and require the addition of stabilization. This can, for example, be done by adding diffusivity near the shock location. One method (used in COMSOL) is to add an effective diffusivity term that reads</a></div>
    <div class="Eqn"><a name="1723957"><img class="Default" src="images/aco_ug_pressure.05.154.3.png" width="126" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1723958">The highest artificial damping is achieved where the acoustic pressure increases or decreases the most rapidly. It reaches its maximal values where the pressure endures discontinuities, that is, where shocks arise. Thus this technique provides a shock-capturing stabilization. See </a><span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1722327" title="References for the Pressure Acoustics Branch">Ref. 52</a></span> for more details. Specify the so-called q-Laplacian exponent <span class="EquationVariables">q</span> and the so-called q-Laplacian factor <span class="Symbol">κ</span> to get the desirable amount of artificial damping. Note that the damping must not be too high nor too low. The particular values of <span class="EquationVariables">q</span> and <span class="Symbol">κ</span> depend on the material and the input signal frequency. The two parameters that control the stabilization require manual tuning. A suggested approach is to use a simple 1D model to tune the parameters based on fluid material properties and frequency content.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1724003"><img class="Default" src="images/aco_ug_pressure.05.154.4.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.564651968504pt">
          <div class="Body_text-middle-cell"><span class="Body_text-ital"><a name="1724008">Nonlinear Acoustics — Modeling of the 1D Westervelt Equation</a></span>: Application Library path <span class="Menu-Bodytext">Acoustics_Module/Nonlinear_Acoustics/nonlinear_acoustics_westervelt_1d</span></div>
        </td>
      </tr>
    </table>
  </body>
</html>