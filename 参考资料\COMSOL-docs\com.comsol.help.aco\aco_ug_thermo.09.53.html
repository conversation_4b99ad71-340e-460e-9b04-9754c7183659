<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Formulation for the Boundary Mode Interface</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="999820">Formulation for the Boundary Mode Interface</a></div>
    <div class="Body_text"><span class="Hyperlink"><a href="aco_ug_thermo.09.33.html#986235" title="The Thermoviscous Acoustics, Boundary Mode Interface" name="999876">The Thermoviscous Acoustics, Boundary Mode Interface</a></span> interface adds extended functionality to the abovementioned mode analysis available in 2D and 1D axisymmetric geometries. The interface can be applied on boundaries in 3D (and 2D axisymmetric) geometries and can be used to compute the propagating modes and out-of-plane wave number <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">n</span> on a (flat) surface of any orientation in 3D. This makes it possible to more easily set up boundary conditions at inlets of waveguides using the mode information.</div>
    <div class="Body_text"><a name="1002231">The equations solved are the same as for </a><span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#954726" title="The Thermoviscous Acoustics, Frequency Domain Interface">The Thermoviscous Acoustics, Frequency Domain Interface</a></span> but using modified expressions for the gradients. The implementation assumes propagation in the out-of-plane direction (that is, in the normal direction <span class="EquationBold">n</span>), and the fields can then be written as</div>
    <div class="Eqn"><a name="1002236"><img class="Default" src="images/aco_ug_thermo.09.53.1.png" width="121" height="99" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1002245">where </a><span class="EquationBold">x</span><span class="EquationConstantsSubscript">ip</span> is the in-plane coordinate, <span class="EquationBold">x</span><span class="EquationConstantsSubscript">op</span> is the out-of-plane coordinate, and <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">n</span> is the wave number that this formulation computes. Because of the above assumption about the acoustic fields, the gradient operator can be split into a tangential in-plane component (||) and an normal out-of-plane component (<span class="Symbol">⊥</span>), such that</div>
    <div class="Eqn"><a name="1000737"><img class="Default" src="images/aco_ug_thermo.09.53.2.png" width="159" height="78" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1002443">The in-plane gradient is defined in COMSOL Multiphysics by the tangential derivative operator </a><span class="Code">dtang()</span>. The divergence of the velocity field is defined as the trace of the Jacobian, that is</div>
    <div class="Eqn"><a name="1002734"><img class="Default" src="images/aco_ug_thermo.09.53.3.png" width="119" height="20" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1002571">With the above modifications and redefinition of the gradient operators, solving for the propagating modes reduces to an eigenvalue problem solving for the acoustic field and the out-of-plane wave number for a given frequency.</a></div>
  </body>
</html>