<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Impedance</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1643635">Impedance</a></div>
    <div class="Body_text"><a name="1643975">The </a><span class="Menu-Bodytext">Impedance </span>condition adds a boundary condition defining the relation between the local acoustic pressure <span class="EquationVariables">p</span> and the normal acoustic velocity <span class="EquationBold">n</span><span class="EquationVariables">·</span><span class="EquationBold">u</span><span class="EquationConstantsSubscript">t</span>, that is, the specific acoustic impedance <span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">i</span>. The model includes the option to define the nonlinear or second order pressure-velocity relation to extend the validity of the impedance condition to higher pressure amplitudes.</div>
    <div class="Body_text"><a name="1645113">The second order impedance expression specifies how the normal velocity relates to the pressure through the impedance, including a correction factor, according to</a></div>
    <div class="Eqn"><a name="1643979"><img class="Default" src="images/aco_ug_ultrasound.10.29.1.png" width="139" height="54" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1643980">This condition can be used to model the properties of artificial boundaries by adding resistive losses (no reactive components can be added). When the impedance is set equal to the characteristic specific impedance of a propagating plane wave </a><span class="Symbol">ρ</span>c, the condition represent the simplest nonreflecting boundary condition. This is also the default value of the impedance when the condition is added.</div>
    <div class="Body_text"><a name="1643981">The acoustic impedance condition (with the default value </a><span class="Symbol">ρ</span>c) should be used at the outer boundary when <span class="Body_text-ital">Absorbing Layers</span> are used in a model.</div>
    <div class="Head3"><a name="1643985">Impedance</a></div>
    <div class="Body_text"><a name="1645355">Select the </a><span class="Menu-Bodytext">Pressure-particle velocity relation</span> used for the impedance formulation <span class="Menu-Bodytext">First order</span> (default) or <span class="Menu-Bodytext">Second order</span>. Enter a value or expression for the <span class="Menu-Bodytext">Impedance </span><span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">i</span> (SI unit: Pa·s/m). The default expression is <span class="Code">nate.c*nate.rho</span> which is the characteristic specific impedance <span class="Symbol">ρ</span>c of a propagating plane wave (linear theory).</div>
  </body>
</html>