<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Assemblies and Pair Conditions</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1639330">Assemblies and Pair Conditions</a></div>
    <div class="Body_text"><a name="1639324">When setting up model based on the discontinuous Galerkin time explicit interfaces it can be advantageous to use assemblies in the geometry and pair conditions in the physics interface. This allows to use non-conforming meshes at the interface between, for example, two materials. Different materials are used in the different parts of the assembly. To couple the parts, at the physics level, the pair boundary conditions should be used. They are in general available from the </a><span class="Menu-Bodytext">Pairs</span> submenu.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1640130"><img class="Default" src="images/aco_ug_ultrasound.10.42.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="1640134">For more detailed information about assemblies see the </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_geometry.19.018.html#648912" title="The Form Union/Assembly Node — Uniting the Geometry">The Form Union/Assembly Node — Uniting the Geometry</a></span> section in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span>.</div>
          <div class="Body_text"><a name="1640197">For more detailed information about the pair features see the </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_modeling.15.61.html#954459" title="Continuity on Interior Boundaries">Continuity on Interior Boundaries</a></span> and <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_definitions.17.100.html#1979576" title="Identity and Contact Pairs">Identity and Contact Pairs</a></span> sections in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span>.</div>
        </td>
      </tr>
    </table>
  </body>
</html>