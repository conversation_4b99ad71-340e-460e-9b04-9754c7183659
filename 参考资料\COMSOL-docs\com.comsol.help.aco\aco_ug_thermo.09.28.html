<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>The Thermoviscous Acoustics, Transient Interface</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head1"><a name="1016762">The Thermoviscous Acoustics, Transient Interface</a></div>
    <div class="Body_text"><a name="1016879">The </a><span class="Menu-Bodytext">Thermoviscous Acoustics, Transient (tatd) </span>interface (<img class="Default" src="images/aco_ug_thermo.09.28.1.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />), found under the <span class="Menu-Bodytext">Thermoviscous Acoustics </span>branch (<img class="Default" src="images/aco_ug_thermo.09.28.2.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) when adding a physics interface, is used to compute the transient evolution of the acoustic variations in pressure, velocity, and temperature. The interface is the time domain equivalent of <span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#954726" title="The Thermoviscous Acoustics, Frequency Domain Interface">The Thermoviscous Acoustics, Frequency Domain Interface</a></span>. This physics interface is required to accurately model acoustics in geometries of small dimensions. Near walls, viscous losses and dissipation due to thermal conduction become important because boundary layers exists. The thicknesses of these boundary layers are known as the viscous and thermal penetration depth. For this reason, it is necessary to include thermal conduction effects and viscous losses explicitly in the governing equations. It is, for example, used when modeling the response of transducers like microphones, miniature loudspeakers and receivers. Other applications include analyzing feedback in hearing aids, smart phones and in mobile devices, or studying the damped vibrations of MEMS structures.</div>
    <div class="Body_text"><a name="1017209">The physics interface solves the equations in the time domain. The model can be extended to model nonlinear effects by adding the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.31.html#1058860" title="Nonlinear Thermoviscous Acoustics Contributions">Nonlinear Thermoviscous Acoustics Contributions</a></span> feature. In the time domain it is also possible to model nonlinear effects due to topology changes, like nonlinear squeeze film damping. This is achieved when combining the interface with the <span class="Body_text-ital">Moving Mesh</span> functionality.</div>
    <div class="Body_text"><a name="1017210">The equations defined by the Thermoviscous Acoustics, Transient interface are the first and second order perturbation formulation of the Navier-Stokes equations in quiescent background conditions solving the continuity, momentum, and energy equations. Due to the detailed description necessary when modeling thermoviscous acoustics, the model simultaneously solves for the acoustic pressure </a><span class="EquationVariables">p</span>, the acoustic velocity variation <span class="EquationBold">u</span> (particle velocity), and the acoustic temperature variations <span class="EquationVariables">T</span>. It is available for 3D, 2D, and 1D Cartesian geometries as well as for 2D and 1D axisymmetric geometries.</div>
    <div class="Body_text"><a name="1017213">The Thermoviscous Acoustics, Transient interface is, as the frequency domain variant, formulated in the so-called scattered field formulation where the total field (subscript </a><span class="EquationConstants">t</span>) is the sum of the scattered field (the field solved for, <span class="EquationVariables">p</span>,<span class="EquationVariables"> </span><span class="EquationBold">u</span>, and <span class="EquationVariables">T</span>) and a possible background acoustic field (subscript <span class="EquationConstants">b</span>), such that</div>
    <div class="Eqn"><a name="1017217"><img class="Default" src="images/aco_ug_thermo.09.28.3.png" width="301" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1017221">The scattered field formulation is not applicable in domains where the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.31.html#1058860" title="Nonlinear Thermoviscous Acoustics Contributions">Nonlinear Thermoviscous Acoustics Contributions</a></span> are included. When no <span class="Hyperlink"><a href="aco_ug_thermo.09.32.html#1021330" title="Background Acoustic Fields">Background Acoustic Fields</a></span> feature is present (the background field values are zero per default) the total field is simply the acoustic field solved for, <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span> = <span class="EquationVariables">p</span>, <span class="EquationBold">u</span><span class="EquationConstantsSubscript">t</span> = <span class="EquationBold">u</span>, and <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">t</span> = <span class="EquationVariables">T</span>. All governing equations and boundary conditions are formulated in the total field variables.</div>
    <div class="Body_text"><a name="1017068">When this physics interface is added, these default nodes are also added to the </a><span class="Menu-Bodytext">Model Builder</span> — <span class="Menu-Bodytext">Thermoviscous Acoustics Model</span>, <span class="Menu-Bodytext">Wall</span>, and <span class="Menu-Bodytext">Initial Values</span>. Then, from the <span class="Menu-Bodytext">Physics</span> toolbar, add other nodes that implement, for example, boundary conditions and sources. You can also right-click <span class="Menu-Bodytext">Thermoviscous Acoustics </span>to select physics features from the context menu.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1017228"><img class="Default" src="images/aco_ug_thermo.09.28.4.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.283398425197pt">
          <div class="Body_text-middle-cell"><a name="1017230">For good modeling strategies, solver suggestions, postprocessing information, as well as tips and tricks, see the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.39.html#973323" title="Modeling with the Thermoviscous Acoustics Branch">Modeling with the Thermoviscous Acoustics Branch</a></span> section.</div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1017493"><img class="Default" src="images/aco_ug_thermo.09.28.5.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.283398425197pt">
          <div class="Body_text-middle-cell"><a name="1017495">For more details about the physics interface see </a><span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#989578" title="The Thermoviscous Acoustics, Frequency Domain Interface">On the Thermoviscous Acoustics Physics Interface</a></span> subsection, under <span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#954726" title="The Thermoviscous Acoustics, Frequency Domain Interface">The Thermoviscous Acoustics, Frequency Domain Interface</a></span> section. Details about the governing equations are found in the <span class="Hyperlink"><a href="aco_ug_thermo.09.46.html#1008569" title="Theory Background for the Thermoviscous Acoustics Branch">Theory Background for the Thermoviscous Acoustics Branch</a></span> section.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1017745">Settings</a></div>
    <div class="Body_text"><a name="1017746">The </a><span class="Menu-Bodytext">Label</span> is the default physics interface name.</div>
    <div class="Body_text"><a name="1017747">The </a><span class="Menu-Bodytext">Name</span> is used primarily as a scope prefix for variables defined by the physics interface. Refer to such physics interface variables in expressions using the pattern <span class="Code">&lt;name&gt;.&lt;variable_name&gt;</span>. In order to distinguish between variables belonging to different physics interfaces, the <span class="Code">name</span> string must be unique. Only letters, numbers, and underscores (_) are permitted in the <span class="Menu-Bodytext">Name</span> field. The first character must be a letter.</div>
    <div class="Body_text"><a name="1017748">The default </a><span class="Menu-Bodytext">Name</span> (for the first physics interface in the model) is <span class="Code">tatd</span>.</div>
    <div class="Head3"><a name="1017749">Equation</a></div>
    <div class="Body_text"><a name="1017750">Expand the </a><span class="Menu-Bodytext">Equation</span> section to see the equations solved for with the <span class="Menu-Bodytext">Equation form</span> specified. The default selection for <span class="Menu-Bodytext">Equation form</span> is set to <span class="Menu-Bodytext">Study controlled</span>. The available studies are selected under <span class="Menu-Bodytext">Show equations assuming</span>.</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1017751">For </a><span class="Menu-Bodytext">Study controlled</span>, the scaling of the equations is optimized for the numerical performance of the different solvers and study types.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1017752">For </a><span class="Menu-Bodytext">Frequency domain</span> you can manually enter the scaling factor <span class="Symbol">Δ</span> under the <span class="Menu-Bodytext">Thermoviscous Acoustics Equation Settings</span> section.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Head3"><a name="1067329">Thermoviscous Acoustics Equations Settings</a></div>
    <div class="Body_text"><a name="1067434">See </a><span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#984434" title="The Thermoviscous Acoustics, Frequency Domain Interface">Thermoviscous Acoustics Equation Settings</a></span>.</div>
    <div class="Head3"><a name="1067381">Stabilization</a></div>
    <div class="Body_text"><a name="1067458">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.28.6.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Stabilization</span>. Select <span class="Menu-Bodytext">No stabilization applied</span> (the default), <span class="Menu-Bodytext">Galerkin least-squares (GLS) stabilization</span>, or <span class="Menu-Bodytext">Streamline upwind Petrov-Galerkin (SUPG) stabilization</span>. When linear thermoviscous acoustic problems are solved, the problem is stable (with the default P1-P2-P2 discretization), but as soon as the <span class="Hyperlink"><a href="aco_ug_thermo.09.31.html#1058860" title="Nonlinear Thermoviscous Acoustics Contributions">Nonlinear Thermoviscous Acoustics Contributions</a></span> feature is used, stabilization may be required. For weakly nonlinear problems, no stabilization is necessary, but for moderate and highly nonlinear problems using stabilization is essential. In most of those cases, use the <span class="Menu-Bodytext">Galerkin least-squares (GLS) stabilization</span> option.</div>
    <div class="Head3"><a name="1026794">Transient solver Settings</a></div>
    <div class="Body_text"><a name="1026790">Enter the </a><span class="Menu-Bodytext">Maximum frequency to resolve</span> in the model. The default frequency is set to <span class="Code">1000[Hz]</span> but should be changed to reflect the frequency content of the sources used in the model. Select the <span class="Menu-Bodytext">Time stepping</span> (method) as <span class="Menu-Bodytext">Fixed (preferred) </span>the default and recommended or <span class="Menu-Bodytext">Free</span>. The <span class="Menu-Bodytext">Free</span> option is in general not recommended for wave problems. The generated solver will be adequate in most situations if the computational mesh also resolves the frequency content in the model. Note that any changes made to these settings (after the model is solved the first time) will only be reflected in the solver if <span class="Menu-Bodytext">Show Default Solver</span> or <span class="Menu-Bodytext">Reset Solver to Defaults </span>is selected in the study. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1026801"><img class="Default" src="images/aco_ug_thermo.09.28.7.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.00214488189pt">
          <div class="Body_text-middle-cell"><a name="1026806">Details about </a><span class="Hyperlink"><a href="aco_ug_thermo.09.43.html#1026968" title="Transient Solver Settings">Transient Solver Settings</a></span> are found in the section <span class="Hyperlink"><a href="aco_ug_thermo.09.39.html#973323" title="Modeling with the Thermoviscous Acoustics Branch">Modeling with the Thermoviscous Acoustics Branch</a></span>.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1017764">Discretization</a></div>
    <div class="Body_text"><a name="1017768">From the list select the element order and type (Lagrange or serendipity) for the </a><span class="Menu-Bodytext">Pressure</span>, the <span class="Menu-Bodytext">Velocity field</span>, and the <span class="Menu-Bodytext">Temperature variation</span>, respectively. The default is <span class="Menu-Bodytext">Linear </span>for the pressure and <span class="Menu-Bodytext">Quadratic serendipity</span> for the velocity and the temperature.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1017776"><img class="Default" src="images/aco_ug_thermo.09.28.8.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Bullets-first_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-first_inner"><a name="1017778">For numerical stability reasons, the element order for the pressure should one less than the element order for the velocity. Unless </a><span class="Hyperlink"><a href="#1067381" title="The Thermoviscous Acoustics, Transient Interface">Stabilization</a></span> is enabled, in which case, for example, a P1-P1-P1 discretization can be used.</div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-inner_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner_inner"><a name="1019141">In fluids where the thermal and viscous boundary layer thickness are of the same order of magnitude (where the Prandtl number </a><span class="EquationConstants">Pr</span> is of the order 1, like in air), it is recommended to use the same shape order for the temperature and the velocity. Both fields vary equally over the same length scale in the acoustic boundary layers near walls.</div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1019407"><img class="Default" src="images/aco_ug_thermo.09.28.9.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.283398425197pt">
          <div class="Body_text-middle-cell"><a name="1019409">Choosing between </a><span class="Hyperlink"><a href="aco_ug_thermo.09.42.html#1019545" title="Lagrange and Serendipity Shape Functions">Lagrange and Serendipity Shape Functions</a></span> has influence on the number of DOFs solved for and on stability for distorted mesh.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1017779">Dependent Variables</a></div>
    <div class="Body_text"><a name="1017422">This physics interface defines these dependent variables (fields), the </a><span class="Menu-Bodytext">Pressure</span> <span class="EquationVariables">p</span>, the <span class="Menu-Bodytext">Velocity field</span> <span class="EquationBold">u</span> and its components, and the <span class="Menu-Bodytext">Temperature variation</span> <span class="EquationVariables">T</span>. The names can be changed but the names of fields and dependent variables must be unique within a model.</div>
  </body>
</html>