<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Initial Values</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="961662">Initial Values</a></div>
    <div class="Body_text"><a name="882208">The </a><span class="Menu-Bodytext">Initial Values </span>node adds initial values for the sound pressure, velocity field, and temperature variation. If necessary, add more <span class="Menu-Bodytext">Initial Values</span> nodes from the <span class="Menu-Bodytext">Physics</span> toolbar to give initial values in, for example, other domains.</div>
    <div class="Head3"><a name="807388">Initial Values</a></div>
    <div class="Body_text"><a name="1042751">Enter values or expressions for the </a><span class="Menu-Bodytext">Pressure</span> <span class="EquationVariables">p</span> (SI unit: Pa), <span class="Menu-Bodytext">Velocity field</span> <span class="EquationBold">u</span> (SI unit: m/s), and<span class="Menu-Bodytext"> Temperature variation </span><span class="EquationVariables">T</span> (SI unit: K).</div>
  </body>
</html>