<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Impedance Conditions</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1395170">Impedance Conditions</a></div>
    <div class="Body_text"><a name="1395213">An impedance boundary condition relates the acoustic pressure and the acoustic velocity to each other at a given point. This relationship is provided by knowledge about the conditions on the boundary, for example, a specific material or an acoustically active boundary. Therefore, an impedance boundary condition can be used to impose the properties of the boundary without modeling it explicitly. Impedance boundary conditions thus generalize the sound-hard and sound-soft boundary conditions to address a large number of cases between these two extremes.</a></div>
    <div class="Body_text"><a name="1410654">Mathematically, a specific acoustic impedance </a><span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">i</span> is defined on some cross section as the ratio between the acoustic pressure <span class="EquationVariables">p</span> and the acoustic velocity perpendicular to the area <img class="Default" src="images/aco_ug_pressure.05.159.1.png" width="21" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /> (the normal velocity)</div>
    <div class="Eqn"><a name="1410107"><img class="Default" src="images/aco_ug_pressure.05.159.2.png" width="56" height="40" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />.</a></div>
    <div class="Body_text"><a name="1395239">In Pressure Acoustics, Frequency Domain, this boundary condition is imposed as</a></div>
    <div class="Equation"> (2-26)<a name="1411154"><img class="Default" src="images/aco_ug_pressure.05.159.3.png" width="186" height="45" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1411155">while in the Pressure Acoustics, Transient interface using a Time Dependent study, the impedance boundary condition is the following:</a></div>
    <div class="Equation"> (2-27)<a name="1411159"><img class="Default" src="images/aco_ug_pressure.05.159.4.png" width="181" height="45" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1440298">Here </a><span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">i</span> is the acoustic input impedance of the external domain and it has the unit of a specific acoustic impedance. The specific acoustic impedance <span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">i</span> (SI unit: Pa·s/m) is related to the acoustic impedance <span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">ac</span> (ratio of pressure and flow rate, SI unit: Pa·s/m<span class="EquationConstantsSuperscript">3</span>) and the mechanical impedance <span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">mech </span>(ratio of force and velocity, SI unit: N·s/m) via the area <span class="EquationVariables">A</span> of the boundary, according to </div>
    <div class="Equation"> (2-28)<a name="1411142"><img class="Default" src="images/aco_ug_pressure.05.159.5.png" width="144" height="29" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1411389">Impedance boundary conditions only relate the normal velocity (the velocity perpendicular to the boundary) to the pressure, but do not consider the tangential velocity (component parallel to the boundary). This is due to the mathematical construction of the governing equation and the fact that pressure acoustics solves only for the scalar pressure. Put differently, the impedance boundary condition only applies to the normal component of the incident field. Thus, by applying an impedance boundary condition this tangential velocity component is ignored altogether. For this reason, impedance boundary conditions are in most cases low-order approximations to the actual boundary properties. In cases where this is unacceptable, it is consequently better to either model the boundary explicitly or use a higher-order model, such as for instance the </a><span class="Hyperlink"><a href="aco_ug_pressure.05.031.html#1389050" title="Plane Wave Radiation">Plane Wave Radiation</a></span> at an open boundary.</div>
  </body>
</html>