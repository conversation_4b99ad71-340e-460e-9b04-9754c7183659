<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Normal Impedance</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="816729">Normal Impedance</a></div>
    <div class="Body_text"><a name="825364">Use the </a><span class="Menu-Bodytext">Normal Impedance </span>node to specify a normal specific impedance <span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">0</span> on a boundary. Add this node from the <span class="Menu-Bodytext">Mechanical </span>submenu. This feature is useful outside the viscous boundary layer, as this condition mimics the behavior of a corresponding <span class="Menu-Bodytext">Pressure Acoustics Model</span> with a normal impedance condition. The boundary condition reads</div>
    <div class="Eqn"><a name="828835"><img class="Default" src="images/aco_ug_thermo.09.23.1.png" width="384" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Head3"><a name="819487">Mechanical</a></div>
    <div class="Body_text"><a name="819517">Enter a value or expression for the</a><span class="Menu-Bodytext"> Normal impedance </span><span class="EquationVariables">Z</span><span class="EquationConstantsSubscript">0</span><span class="EquationVariablesSubscript"> </span>(SI unit: Pa<span class="Symbol">⋅</span>s/m).</div>
    <div class="Body_text"><a name="1064538">Select a condition for the </a><span class="Menu-Bodytext">Tangential velocity</span> — <span class="Menu-Bodytext">Slip</span> (the default) or <span class="Menu-Bodytext">No slip</span>. The <span class="Menu-Bodytext">No Slip</span> option will apply an additional constraint for the velocity in the tangential direction on the boundary.</div>
  </body>
</html>