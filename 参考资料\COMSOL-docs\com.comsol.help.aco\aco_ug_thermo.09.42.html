<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title><PERSON><PERSON><PERSON> and Serendipity Shape Functions</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1019545">Lagrange and Serendipity Shape Functions</a></div>
    <div class="Body_text"><a name="1019546">In most of the physics interfaces in the Acoustics Module, and specifically in the Thermoviscous Acoustic interfaces, you can choose between two families of shape functions in the </a><span class="Menu-Bodytext">Discretization</span> section: <span class="Body_text-ital">Lagrange</span> and <span class="Body_text-ital">serendipity</span>. The current default is to use serendipity shape functions.</div>
    <div class="Body_text"><a name="1019547">When using a structured mesh, it is advantageous to use the serendipity elements as they generate significantly fewer degrees of freedom (DOFs). The accuracy is in most cases almost as good as for the Lagrange elements. The Lagrange elements are, however, less sensitive to strong mesh distortions.</a></div>
    <div class="Body_text"><a name="1019548">The serendipity shape functions differ from the Lagrange shape functions only for the following element shapes:</a></div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1019549">2D: Quadrilateral elements of discretization order higher than 1.</a></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1019566">3D: Hexahedral, prism, and pyramid elements of discretization order higher than 1.</a></div>
          </td>
        </tr>
      </table>
    </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1019555"><img class="Default" src="images/aco_ug_thermo.09.42.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.283398425197pt">
          <div class="Body_text-middle-cell"><a name="1019557">In the </a><span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span>:</div>
          <div class="Bullets-first-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-first-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-first-cell_inner"><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_api_xmesh.46.3.html#5181733" title="Shape Function Types (Elements)" name="1019561">The Lagrange Element (shlag)</a></span></div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-last-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-last-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-last-cell_inner"><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_api_xmesh.46.3.html#5181754" title="Shape Function Types (Elements)" name="1019565">The Nodal Serendipity Element (shnserp)</a></span></div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="1019541">When coupling two physics interfaces that have the same DOFs like, for example, displacement, the same type of shape functions should be used in both interfaces to ensure conformity. Since there is no difference between the two families of shape functions in 1D, this is not an issue when connecting edges.</a></div>
  </body>
</html>