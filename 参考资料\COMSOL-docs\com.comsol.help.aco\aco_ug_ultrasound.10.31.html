<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Interior Normal Velocity</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1645700">Interior Normal Velocity</a></div>
    <div class="Body_text"><a name="1645734">The </a><span class="Menu-Bodytext">Interior Normal Velocity </span>node, found under the <span class="Menu-Bodytext">Interior Conditions </span>submenu, adds the boundary condition for a moving interior boundary. This can, for example, represent a thin vibrating structure or s speaker membrane. The condition reads</div>
    <div class="Eqn"><a name="1645738"><img class="Default" src="images/aco_ug_ultrasound.10.31.1.png" width="207" height="48" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1645739">The condition ensures that the total normal velocity is defined on both the up and down side of the boundary, while applying a slit on the pressure (the pressure is discontinuous across the boundary).</a></div>
    <div class="Head3"><a name="1645740">Normal Velocity</a></div>
    <div class="Body_text"><a name="1645730">Select the </a><span class="Menu-Bodytext">Type</span>: <span class="Menu-Bodytext">Velocity</span> (default) or <span class="Menu-Bodytext">Normal velocity</span> and specify either the <span class="Menu-Bodytext">Velocity</span> (vector) <span class="EquationBold">v</span><span class="EquationConstantsSubscript">0</span> or the normal component through the <span class="Menu-Bodytext">Normal velocity</span> <span class="EquationVariables">v</span><span class="EquationConstantsSubscript">n</span>.</div>
  </body>
</html>