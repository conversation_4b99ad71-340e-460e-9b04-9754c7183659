<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Hybrid Computational Aeroacoustics (CAA)</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1914131">Hybrid Computational Aeroacoustics (CAA)</a></div>
    <div class="Body_text"><a name="1915591">The computational aeroacoustic (CAA) method used here is of the so-called hybrid type. It is based on a one way coupling between the turbulent flow sources and the acoustic problem. It is thus assumed that no important back-coupling exists from the acoustic field to the flow. The computational method is based on the finite element method (FEM) discretization of Lighthill’s acoustic analogy (wave equation). This formulation of the equations ensures that any solid (fixed or vibrating) boundaries are implicitly taken into account. The method is also applicable for interior problems (see </a><span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1916010" title="References for the Pressure Acoustics Branch">Ref. 65</a></span>).</div>
    <div class="Body_text"><a name="1921303">The hybrid method is based on a mapping and time to frequency FFT approach, in order to solve the acoustic problem in the frequency domain and on a mesh appropriate for the acoustic problem. The mesh used to solve the LES flow problem has characteristics necessary to resolve the turbulence as well as the flow boundary layers. The mesh also corresponds to linear discretization for most LES methods. The acoustic mesh, on the other hand, has to resolve the wavelength and geometry, and it is typically based on the default second order spatial discretization (see </a><span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1916010" title="References for the Pressure Acoustics Branch">Ref. 65</a></span> and <span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1916176" title="References for the Pressure Acoustics Branch">Ref. 66</a></span>).</div>
  </body>
</html>