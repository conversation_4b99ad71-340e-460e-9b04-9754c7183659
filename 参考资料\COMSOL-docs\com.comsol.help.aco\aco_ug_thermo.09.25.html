<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Adiabatic</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="816916">Adiabatic</a></div>
    <div class="Body_text"><a name="818217">Use the </a><span class="Menu-Bodytext">Adiabatic </span>node to define a situation with no heat flow into or out of the boundary. Add this node from the <span class="Menu-Bodytext">Thermal </span>submenu. The condition reads</div>
    <div class="Eqn"><a name="828861"><img class="Default" src="images/aco_ug_thermo.09.25.1.png" width="111" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
  </body>
</html>