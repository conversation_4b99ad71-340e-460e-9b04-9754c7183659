<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Convected Wave Equation Model</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1603060">Convected Wave Equation Model</a></div>
    <div class="Body_text"><a name="1603115">The </a><span class="Menu-Bodytext">Convected Wave Equations </span>node adds the equations for modeling the propagation of acoustic waves in a stationary background flow. Adiabatic behavior is assumed, meaning that this in some sense represents pressure acoustics in the presence of flow. The convected wave equation model solves the linearized Euler equations also referred to as linear acoustic equations for moving media. The equations are valid for any stationary background mean flow as long as there are not too large gradients in the background properties.</div>
    <div class="Body_text"><a name="1613594">The linear continuity equation, momentum equation, and equation of state solved are given by:</a></div>
    <div class="Eqn"><a name="1613559"><img class="Default" src="images/aco_ug_ultrasound.10.04.1.png" width="341" height="132" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1613832">where </a><span class="EquationBold">u</span><span class="EquationConstantsSubscript">0</span> is the background mean flow velocity, <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span> is the background mean flow pressure, <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span> is the background mean flow density, and c<span class="EquationConstantsSubscript">0</span> is the speed of sound. All background properties can vary in space (the variation has to be smooth), but cannot be discontinuous. The right-hand side sources <span class="EquationVariables">f</span><span class="EquationConstantsSubscript">p</span> and <span class="EquationBold">f</span><span class="EquationConstantsSubscript">v</span> can be defined by the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.05.html#1604526" title="Domain Sources">Domain Sources</a></span>. More information about the governing equations is given in the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.46.html#1430317" title="Theory for the Convected Wave Equation Interface">Theory for the Convected Wave Equation Interface</a></span> section.</div>
    <div class="Body_text"><a name="1613335">In the </a><span class="Menu-Bodytext">Settings</span> window, define the properties for the acoustics model and model inputs including the background mean flow, pressure, and velocity.</div>
    <div class="Head3"><a name="1603145">Model Inputs</a></div>
    <div class="Body_text"><a name="1614518">In order to model the influence the background mean flow has on the propagation of the acoustic waves in the fluid, the </a><span class="Menu-Bodytext">Background mean flow pressure</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span> and <span class="Menu-Bodytext">Background mean flow velocity</span> <span class="EquationBold">u</span><span class="EquationConstantsSubscript">0</span> need to be defined. If a material that is temperature dependent is used the <span class="Menu-Bodytext">Background mean flow temperature</span> <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span> field is also present.</div>
    <div class="Body_text"><a name="1603157">All the background mean flow parameters can be functions of space. They can be either analytical expressions (user defined) or they can be picked up from a fluid flow simulation performed using the CFD Module. In this case use the </a><span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.12.html#655094" title="Background Fluid Flow Coupling">Background Fluid Flow Coupling</a></span> multiphysics feature to ensure correct mapping of the flow data. By default they are set to the quiescent constant background conditions of air.</div>
    <div class="Head3"><a name="1603171">Fluid Properties</a></div>
    <div class="Body_text"><a name="1641235">Select a </a><span class="Menu-Bodytext">Fluid model</span> either as <span class="Menu-Bodytext">Linear elastic</span> (the default) or <span class="Menu-Bodytext">General dissipation</span>. For both options define the background mean flow density <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span> (SI unit: kg/m<span class="Superscript-Bodytext">3</span>) and the background mean flow speed of sound <span class="EquationVariables">c</span><span class="EquationConstantsSubscript">0</span> (SI unit: m/s). The default is to use the property values from the material (<span class="Menu-Bodytext">From material</span>). Select <span class="Menu-Bodytext">User defined</span> from the list to enter a user-defined value in the text field that appears. For the <span class="Menu-Bodytext">General dissipation</span> option also define the <span class="Menu-Bodytext">Sound diffusivity</span> <span class="Symbol">δ</span> (SI unit: m<span class="Superscript-Bodytext">2</span>/s). For numerical stability reasons it is recommended to use physical values of attenuation properties.</div>
    <div class="Body_text"><a name="1641137">The density can also be picked up from a fluid flow simulation when the </a><span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.12.html#655094" title="Background Fluid Flow Coupling">Background Fluid Flow Coupling</a></span> multiphysics feature is used.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.5413291338583pt">
          <div class="img-tables"><a name="1651190"><img class="Default" src="images/aco_ug_ultrasound.10.04.2.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.031681889764pt">
          <div class="Body_text"><a name="1651159">The relation between the sound diffusivity </a><span class="Symbol">δ </span>and the equivalent (plane wave) attenuation coefficient <span class="Symbol">α</span>, at a given frequency <span class="EquationVariables">f</span>, is given by the expression <img class="Default" src="images/aco_ug_ultrasound.10.04.3.png" width="102" height="25" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />, where <span class="Symbol">ω</span> = 2<span class="Symbol">π</span><span class="EquationVariables">f</span> and <span class="EquationVariables">c</span> is the speed of sound.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1603929">Lax–Friedrichs Flux Parameter</a></div>
    <div class="Body_text"><a name="1606440">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_ultrasound.10.04.4.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Stabilization</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. In this section, you specify the value of the <span class="Body_text-ital">Lax–Friedrichs flux parameter</span> <span class="Symbol">τ</span><span class="EquationConstantsSubscript">LF</span> (default value: 0.2). This value controls the numerical flux between the elements (nodal discontinuous Lagrange elements) used with the discontinuous Galerkin (dG) method. The numerical flux defines how adjacent elements are connected and how continuous <span class="EquationVariables">p</span> and <span class="EquationBold">u</span> are. Different definitions of the numerical flux lead to different variants of the dG method. The flux implemented here is the so-called global Lax–Friedrichs numerical flux. The value of the parameter <span class="Symbol">τ</span><span class="EquationConstantsSubscript">LF </span>should be between 0 and 0.5. For <span class="Symbol">τ</span><span class="EquationConstantsSubscript">LF </span>= 0 a so-called central flux is obtained. Setting <span class="Symbol">τ</span><span class="EquationConstantsSubscript">LF </span>= 0.5 gives a maximally dissipative global Lax–Friedrichs flux.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1607521"><img class="Default" src="images/aco_ug_ultrasound.10.04.5.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="1607526">For general information about the numerical flux see the </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_equationbased.28.059.html#1149047" title="Wave Form PDE">Numerical Flux</a></span> section under <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_equationbased.28.059.html#1149030" title="Wave Form PDE">Wave Form PDE</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual.</span></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1604160">Filter Parameters</a></div>
    <div class="Body_text"><a name="1606416">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_ultrasound.10.04.6.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. By default, the filter parameters <span class="Symbol">α</span>, <span class="Symbol">η</span><span class="EquationConstantsSubscript">c</span>, and <span class="EquationVariables">s</span> are not active. Select the <span class="Menu-Bodytext">Activate</span> check box to activate the filter. The filter provides higher-order smoothing for the dG formulation and can be used to stabilize the solution, for example, when a large background flow is present or large gradients are present. Inside absorbing layers the settings given here are overridden by the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.02.html#1605371" title="The Convected Wave Equation, Time Explicit Interface">Filter Parameters for Absorbing Layers</a></span>.</div>
    <div class="Body_text"><a name="1616036">Enter values for the filter parameters in the corresponding text fields (default values: 36, 0.6, and 3). </a><span class="Symbol">α</span> must be positive and lie between 0 and 36. <span class="Symbol">α </span>= 0 means no dissipation and <span class="Symbol">α </span>= 36 means maximal dissipation. <span class="Symbol">η</span><span class="EquationConstantsSubscript">c</span> should be between 0 and 1, where 0 means maximal filtering and 1 means no filtering (even if filtering is active). The <span class="EquationVariables">s</span> parameter should be larger than 0 and controls the order of the filtering (a dissipation operator of order 2<span class="EquationVariables">s</span>). For <span class="EquationVariables">s</span> = 1, you get a filter that is related to the classical 2nd-order Laplacian. A larger s gives a more pronounced low-pass filter.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1607490"><img class="Default" src="images/aco_ug_ultrasound.10.04.7.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="1607495">For more detailed information about the filter see the </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_equationbased.28.059.html#1149080" title="Wave Form PDE">Filter Parameters</a></span> section under <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_equationbased.28.059.html#1149030" title="Wave Form PDE">Wave Form PDE</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual.</span></div>
        </td>
      </tr>
    </table>
  </body>
</html>