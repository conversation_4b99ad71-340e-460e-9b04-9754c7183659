<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Postprocessing: Variables and Quality</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1625477">Postprocessing: Variables and Quality</a></div>
    <div class="Head3"><a name="1625484">Postprocessing Variables</a></div>
    <div class="Body_text"><a name="1625515">Several variables are defined to be used when postprocessing. They include the pressure, velocity components, and the norm of the velocity, as well as material parameters. The instantaneous intensity also exists as a variable (</a><span class="Code">cwe.Ii</span>). It is defined as</div>
    <div class="Eqn"><a name="1625796"><img class="Default" src="images/aco_ug_ultrasound.10.38.1.png" width="187" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1625807">Notice that the instantaneous intensity differs from the usual intensity which is an averaged value.</a></div>
    <div class="Head3"><a name="1625478">Quality</a></div>
    <div class="Body_text"><a name="1625479">When analyzing the results from a simulation with the CWE interface, be aware of the fact that fourth order elements are used to discretize the dependent variables (as discussed in the section above). This, in some sense, means that within a mesh element, the shape function has a lot of freedom and can contain a lot of details. These details are revealed by setting a high </a><span class="Menu-Bodytext">Resolution</span> in the <span class="Menu-Bodytext">Quality</span> section in the plots. The default plots generated already have that option set. If you add more user defined plots, remember to set the resolution.</div>
  </body>
</html>