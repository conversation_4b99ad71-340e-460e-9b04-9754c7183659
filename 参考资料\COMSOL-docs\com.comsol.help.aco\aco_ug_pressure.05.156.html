<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Theory for the Plane, Spherical, and Cylindrical Radiation Boundary Conditions</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1135904">Theory for the Plane, Spherical, and Cylindrical Radiation Boundary Conditions</a></div>
    <div class="Body_text"><a name="1135907">Specify a </a><span class="Hyperlink"><a href="aco_ug_pressure.05.031.html#1389050" title="Plane Wave Radiation">Plane Wave Radiation</a></span>, <span class="Hyperlink"><a href="aco_ug_pressure.05.032.html#1389094" title="Spherical Wave Radiation">Spherical Wave Radiation</a></span>, or <span class="Hyperlink"><a href="aco_ug_pressure.05.033.html#1389121" title="Cylindrical Wave Radiation">Cylindrical Wave Radiation</a></span><span class="Menu-Bodytext"> </span>boundary condition to allow an outgoing wave to leave the modeling domain with minimal reflections. The condition can be adapted to the geometry of the modeling domain. The <span class="Body_text-ital">plane wave</span> type is suitable for both far-field boundaries and ports (for plane waves only). For general radiation boundary conditions for waveguides (supporting multi-modes) it is recommended to use the <span class="Hyperlink"><a href="aco_ug_pressure.05.026.html#1691211" title="Port">Port</a></span> boundary condition.</div>
    <div class="Body_text"><a name="1135916">Radiation boundary conditions are available for all types of studies. For the frequency-domain study, Givoli and Neta’s reformulation of the Higdon conditions (</a><span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203174" title="References for the Pressure Acoustics Branch">Ref. 1</a></span>) for plane waves has been implemented to the second order. For cylindrical and spherical waves, COMSOL Multiphysics<span class="Superscript-Bodytext"> </span>uses the corresponding 2nd-order expressions from Bayliss, Gunzburger, and Turkel (<span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203176" title="References for the Pressure Acoustics Branch">Ref. 2</a></span>). The Transient, Mode analysis, and Eigenfrequency studies implement the same expansions to the first order.</div>
    <div class="Body_text"><a name="1135930">The first-order radiation boundary conditions in the frequency domain read</a></div>
    <div class="Eqn"><a name="1135934"><img class="Default" src="images/aco_ug_pressure.05.156.1.png" width="388" height="45" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1135935">where </a><span class="EquationVariables">k</span> is the wave number and <span class="Symbol">κ</span><span class="EquationVariables"> </span><span class="EquationConstants">(</span><span class="EquationVariables"> r </span><span class="EquationConstants">)</span> is a function whose form depends on the wave type:</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1135936">Plane wave: </a><span class="Symbol">κ</span>( <span class="EquationVariables">r</span> )<span class="Symbol"> = </span><span class="EquationConstants">0</span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1135937">Cylindrical wave: </a><span class="Symbol">κ</span>( <span class="EquationVariables">r</span> )<span class="Symbol"> = </span><span class="EquationConstants">1</span><span class="Symbol"> / </span>(<span class="EquationConstants">2</span> <span class="EquationVariables">r</span>)</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1135938">Spherical wave: </a><span class="Symbol">κ</span>( <span class="EquationVariables">r</span> )<span class="Symbol"> = </span><span class="EquationConstants">1</span><span class="Symbol"> / </span><span class="EquationVariables">r</span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Body_text"><a name="1135939">In the cylindrical and spherical wave cases, </a><span class="EquationVariables">r</span> is the shortest distance from the point <span class="EquationBold">r</span><span class="Symbol"> = </span>(<span class="EquationVariables">x</span>, <span class="EquationVariables">y</span>, <span class="EquationVariables">z</span>) on the boundary to the source. The right-hand side of the equation represents an optional incoming pressure field <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">i</span> (see <span class="Hyperlink"><a href="aco_ug_pressure.05.034.html#1389148" title="Incident Pressure Field">Incident Pressure Field</a></span>).</div>
    <div class="Body_text"><a name="1135943">The second-order radiation boundary conditions in the frequency domain are defined below. In these equations, <img class="Default" src="images/aco_ug_pressure.05.156.2.png" width="22" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />at a given point on the boundary denotes the Laplace operator in the tangent plane at that particular point.</a></div>
    <div class="Head3"><a name="1135945">Plane Wave</a></div>
    <div class="Eqn"><a name="1135949"><img class="Default" src="images/aco_ug_pressure.05.156.3.png" width="432" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1135956">In the notation of Givoli and Neta (</a><span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203174" title="References for the Pressure Acoustics Branch">Ref. 1</a></span>), the above expressions correspond to the parameter choices <span class="EquationVariables">C</span><span class="EquationConstantsSubscript">0</span> <span class="Symbol">=</span> <span class="EquationVariables">C</span><span class="EquationConstantsSubscript">1</span> <span class="Symbol">=</span> <span class="EquationVariables">C</span><span class="EquationConstantsSubscript">2</span> <span class="Symbol">=</span> <span class="Symbol">ω</span>/<span class="EquationVariables">k</span>. For normally incident waves, this gives a vanishing reflection coefficient.</div>
    <div class="Head3"><a name="1135957">Cylindrical Wave</a></div>
    <div class="Eqn"><a name="1135961"><img class="Default" src="images/aco_ug_pressure.05.156.4.png" width="388" height="99" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1135963">The </a><span class="Body_text-ital">cylindrical wave</span> boundary condition is based on a series expansion of the outgoing wave in cylindrical coordinates (<span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203176" title="References for the Pressure Acoustics Branch">Ref. 2</a></span>), and it assumes that the field is independent of the axial coordinate. Specify the axis of this coordinate system by giving an orientation <span class="EquationConstants">(</span><span class="EquationVariables">n</span><span class="EquationVariablesSubscript">x</span>,  <span class="EquationVariables">n</span><span class="EquationVariablesSubscript">y</span>, <span class="EquationVariables">n</span><span class="EquationVariablesSubscript">z</span>) and a point <span class="EquationConstants">(</span><span class="EquationVariables">x</span><span class="EquationConstantsSubscript">0</span>, <span class="EquationVariables">y</span><span class="EquationConstantsSubscript">0</span>, <span class="EquationVariables">z</span><span class="EquationConstantsSubscript">0</span>) on the axis. In axisymmetric geometries, the symmetry axis is the natural and only choice.</div>
    <div class="Head3"><a name="1135970">Spherical Wave</a></div>
    <div class="Eqn"><a name="1135974"><img class="Default" src="images/aco_ug_pressure.05.156.5.png" width="318" height="90" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1135976">Use a </a><span class="Body_text-ital">spherical wave</span> to allow a radiated or scattered wave — emanating from an object centered at the point <span class="EquationConstants">(</span><span class="EquationVariables">x</span><span class="EquationConstantsSubscript">0</span>, <span class="EquationVariables">y</span><span class="EquationConstantsSubscript">0</span>, <span class="EquationVariables">z</span><span class="EquationConstantsSubscript">0</span>) that is specified — to leave the modeling domain without reflections. The boundary condition is based on an expansion in spherical coordinates from Bayliss, Gunzburger, and Turkel (<span class="Hyperlink"><a href="aco_ug_pressure.05.175.html#1203176" title="References for the Pressure Acoustics Branch">Ref. 2</a></span>), implemented to the second order.</div>
    <div class="Head3"><a name="1135983">Transient Analysis</a></div>
    <div class="Body_text"><a name="1135985">The transient radiation boundary condition is the first-order expression</a></div>
    <div class="Eqn"><a name="1135989"><img class="Default" src="images/aco_ug_pressure.05.156.6.png" width="411" height="46" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1135990">where </a><span class="Symbol">κ</span><span class="EquationVariables"> </span><span class="EquationConstants">(</span><span class="EquationVariables"> r </span><span class="EquationConstants">)</span> is the same wave-type dependent function as for the eigenfrequency case and <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">i</span> the optional <span class="Hyperlink"><a href="aco_ug_pressure.05.034.html#1389148" title="Incident Pressure Field">Incident Pressure Field</a></span>.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1135999"><img class="Default" src="images/aco_ug_pressure.05.156.7.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.00214488189pt">
          <div class="Body_text-middle-cell"><a name="1136001">An estimate of the reflection coefficient </a><span class="EquationVariables">R</span><span class="EquationVariablesSubscript">s</span> for spurious waves off the plane wave radiation boundary, for incident plane waves at angle <span class="Symbol">θ</span> is given by the expression:</div>
          <div class="EqnTable"><a name="1136005"><img class="Default" src="images/aco_ug_pressure.05.156.8.png" width="111" height="39" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
          <div class="Body_text-middle-cell"><a name="1136006">where </a><span class="EquationVariables">N</span> is the order of the boundary condition (here 1 or 2). So at normal incidence (<span class="Symbol">θ </span>= 0) there are no spurious reflections, while, for example, at an incidence angle of 30<span class="EquationConstantsSuperscript">o</span> for <span class="EquationVariables">N</span> = 2 (plane wave radiation in the frequency domain) the amplitude of the spurious reflected wave is 0.5 % of the incident.</div>
        </td>
      </tr>
    </table>
  </body>
</html>