<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Pressure Acoustics, Boundary Mode Equations</title>
    <link rel="StyleSheet" href="css/aco_ug_pressure.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="805702">Pressure Acoustics, Boundary Mode Equations</a></div>
    <div class="Body_text"><a name="805704">When an acoustic wave of a given angular frequency </a><span class="Symbol">ω</span> is fed into a waveguide or a duct, only a finite set of shapes, or modes, for the transverse pressure field can propagate over long distances inside the structure. The higher the frequency, the higher the number of sustainable modes.</div>
    <div class="Body_text"><a name="641228">Take, as an example, a uniform straight duct whose axis is in the </a><span class="EquationVariables">z</span> direction. The acoustic field in such a duct can be written as a sum of the form</div>
    <div class="Eqn"><a name="641232"><img class="Default" src="images/aco_ug_pressure.05.155.1.png" width="158" height="63" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="641234">The constant </a><span class="EquationVariables">k</span><span class="EquationVariablesSubscript">zj</span> is the axial wave number of the <span class="EquationVariables">j</span>th propagating transverse mode, <span class="EquationVariables">p</span><span class="EquationVariablesSubscript">j</span>(<span class="EquationVariables">x</span>, <span class="EquationVariables">y</span><span class="EquationConstants">)</span>. These transverse modes and their associated axial wave numbers are solutions to an eigenvalue problem defined on the duct’s cross section. The mode analysis capabilities in <span class="Hyperlink"><a href="aco_ug_pressure.05.066.html#559800" title="The Pressure Acoustics, Boundary Mode Interface">The Pressure Acoustics, Boundary Mode Interface</a></span> makes it possible to solve such eigenvalue problems. The physics interface is available for 3D Cartesian and 2D axisymmetric geometries and solves for the transverse eigenmodes for the acoustic pressure <span class="EquationVariables">p</span> and the associated propagation constants <span class="EquationVariables">k</span><span class="EquationVariablesSubscript">z</span>. The <span class="Hyperlink"><a href="#641237" title="Pressure Acoustics, Boundary Mode Equations">Mode Analysis Study</a></span> is briefly discussed.</div>
    <div class="Head3"><a name="641237">Mode Analysis Study</a></div>
    <div class="Body_text"><a name="641238">The eigenvalue solver computes a specified number of solutions </a><span class="Symbol">{</span><span class="EquationVariables">p</span><span class="EquationVariablesSubscript">j</span>, <span class="Symbol">λ</span><span class="EquationVariablesSubscript">j</span><span class="Symbol">}</span><span class="EquationConstants"> </span>to the equation</div>
    <div class="Equation"> (2-22)<a name="641243"><img class="Default" src="images/aco_ug_pressure.05.155.2.png" width="249" height="54" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="641244">defined on a 2D boundary of the modeling domain (in 3D) or on the 2D domain itself, with </a><span class="Symbol">λ </span>=<span class="Symbol"> −</span><span class="EquationVariables">ik</span><span class="EquationConstantsSubscript">n</span> as the eigenvalue. In this equation, <span class="EquationVariables">p</span> is the in-plane pressure, <span class="Symbol">ρ</span><span class="EquationVariablesSubscript">c</span> is the density, <span class="EquationVariables">c</span><span class="EquationVariablesSubscript">c</span> is the speed of sound, <span class="Symbol">ω</span> is the angular frequency, and <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">n</span> is the propagation constant in the direction normal to the surface, in this context also referred to as the out-of-plane wave number.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="800421"><img class="Default" src="images/aco_ug_pressure.05.155.3.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.00214488189pt">
          <div class="Body_text-middle-cell"><a name="800423">The out-of-plane wave number is denoted </a><span class="EquationVariables">k</span><span class="EquationConstantsSubscript">n</span>, and is in the normal direction to the two-dimensional surface on which <span class="Hyperlink"><a href="#641243" title="Pressure Acoustics, Boundary Mode Equations">Equation 2-22</a></span>. As for a mode analysis study in the frequency domain the propagation direction does not necessarily have to be normal to the  <span class="EquationVariables">z</span>-axis for 3D geometries.</div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1181689"><img class="Default" src="images/aco_ug_pressure.05.155.4.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.00214488189pt">
          <div class="Body_text-middle-cell"><a name="1181691">Special postprocessing variables exist for the Pressure Acoustics, Boundary Mode interface. They are described in </a><span class="Hyperlink"><a href="aco_ug_pressure.05.129.html#1457834" title="Postprocessing Variables">Pressure Acoustics, Boundary Mode Variables</a></span>.</div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="641251">Notice that the above equation is identical to the time-harmonic equation for pressure acoustics, except that </a><span class="EquationVariables">k</span><span class="EquationConstantsSubscript">n</span><span class="EquationVariables"> </span>is interpreted as an eigenvalue and not as a parameter.</div>
    <div class="Body_text"><a name="641252">For axisymmetric geometries, the relevant eigenvalue equation to solve for the radial pressure modes and the eigenvalues </a><span class="Symbol">λ</span> is</div>
    <div class="Eqn"><a name="641256"><img class="Default" src="images/aco_ug_pressure.05.155.5.png" width="238" height="45" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1135877">Here </a><span class="EquationVariables">m</span>, the<span class="Body_text-ital"> azimuthal mode number</span>, is an integer-valued parameter. The equation is defined on the interval <span class="EquationVariables">r</span><span class="EquationConstantsSubscript">1</span><span class="Symbol"> &lt; </span><span class="EquationVariables">r</span><span class="Symbol"> &lt; </span><span class="EquationVariables">r</span><span class="EquationConstantsSubscript">2</span>. The eigenvalue <span class="Symbol">λ</span> is defined in terms of the axial wave number <span class="EquationVariables">k</span><span class="EquationVariablesSubscript">z</span> through the equation <span class="Symbol">λ = −</span><span class="EquationVariables">ik</span><span class="EquationVariablesSubscript">z</span></div>
  </body>
</html>