<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Storing Solution on Selections for Large Models</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1616790">Storing Solution on Selections for Large Models</a></div>
    <div class="Body_text"><a name="1616834">Since the CWE interface is suited for solving large acoustic problems (measured in the number of wavelengths per geometry unit it can handle), the model solved can easily contain many million degrees of freedom (DOFs). Storing the solution with a desired time resolution can thus results in huge data files. To circumvent this, a good practice is to use the </a><span class="Menu-Bodytext">Store fields in output</span> option found under the <span class="Menu-Bodytext">Values of Dependent Variables</span> section in the main <span class="Menu-Bodytext">Time Dependent</span> solver (for example, in the <span class="Menu-Bodytext">Study 1&gt;Step 1: Time Dependent </span>node). Using the <span class="Menu-Bodytext">For selections</span> option, it is possible to only store data on predefined selections. This can, for example, be on a symmetry plane, along an edge, or at the location of a receiver in a model. When postprocessing the data, best results are obtained by adding the same selections to the dataset used.</div>
  </body>
</html>