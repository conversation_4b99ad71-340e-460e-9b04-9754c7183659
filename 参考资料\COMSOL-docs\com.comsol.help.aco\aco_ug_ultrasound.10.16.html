<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>The Nonlinear Pressure Acoustics, Time Explicit Interface</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head1"><a name="1642254">The Nonlinear Pressure Acoustics, Time Explicit Interface</a></div>
    <div class="Body_text"><a name="1642358">The </a><span class="Menu-Bodytext">Nonlinear Pressure Acoustics, Time Explicit (nate) </span>interface (<img class="Default" src="images/aco_ug_ultrasound.10.16.1.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />), found under the <span class="Menu-Bodytext">Acoustics&gt;Ultrasound</span> subbranch (<img class="Default" src="images/aco_ug_ultrasound.10.16.2.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) when adding a physics interface, is used to model the propagation of nonlinear finite amplitude acoustic waves in computational domains that contain many wavelengths. It is suited for time-dependent simulations with arbitrary time-dependent sources and fields. Absorbing layers are used to set up effective nonreflecting-like boundary conditions. Application areas include biomedical applications, for instance, ultrasonic imaging and high-intensity focused ultrasound (HIFU). The applications are not restricted to ultrasound. The interface exists in 2D, 2D axisymmetric, and 3D.</div>
    <div class="Body_text"><a name="1642362">The interface is based on the discontinuous Galerkin (dG or dG-FEM) method and uses a time explicit solver. The method is very memory efficient and can solve problems with many million degrees of freedom (DOFs). The method is also well suited for distributed computing on clusters.</a></div>
    <div class="Body_text"><a name="1642347">In the linear case it can be advantageous to use </a><span class="Hyperlink"><a href="aco_ug_pressure.05.086.html#1581128" title="The Pressure Acoustics, Time Explicit Interface">The Pressure Acoustics, Time Explicit Interface</a></span> instead, for example, when modeling scattering phenomena using a scattered field formulation and a background acoustic field. This split is not possible for nonlinear problems. In the presence of a stationary background flow and linear propagation the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.02.html#1447802" title="The Convected Wave Equation, Time Explicit Interface">The Convected Wave Equation, Time Explicit Interface</a></span> should be used, for example, when modeling ultrasonic flow meters.</div>
    <div class="Body_text"><a name="1658448">For modeling acoustic-structure interaction (ASI) or vibroacoustic problems the interface is fully multiphysics enabled and can be coupled to the </a><span class="Hyperlink"><a href="aco_ug_elastic_waves.06.17.html#1764896" title="The Elastic Waves, Time Explicit Interface">The Elastic Waves, Time Explicit Interface</a></span>, using either the <span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.16.html#650082" title="Acoustic-Structure Boundary, Time Explicit">Acoustic-Structure Boundary, Time Explicit</a></span> or the <span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.17.html#650135" title="Pair Acoustic-Structure Boundary, Time Explicit">Pair Acoustic-Structure Boundary, Time Explicit</a></span> multiphysics couplings.</div>
    <div class="Body_text"><a name="1642862">The interface solves the second-order nonlinear governing equations for the acoustic pressure </a><span class="EquationVariables">p </span>and the acoustic velocity perturbations <span class="EquationBold">u</span>. The interface is suited for modeling progressive wave propagation phenomena when the cumulative nonlinear effects surpass the local nonlinear effects. Thus the model is consistent with the second-order Westervelt equation for the acoustic pressure (see also the <span class="Hyperlink"><a href="aco_ug_pressure.05.055.html#1666591" title="Nonlinear Acoustics (Westervelt) Contributions">Nonlinear Acoustics (Westervelt) Contributions</a></span> feature available with <span class="Hyperlink"><a href="aco_ug_pressure.05.052.html#1091943" title="The Pressure Acoustics, Transient Interface">The Pressure Acoustics, Transient Interface</a></span>). General bulk dissipation (volumetric damping) can be added to model real fluids.</div>
    <div class="Body_text"><a name="1652914">Several features are available to help solve the nonlinear and highly nonlinear problems including the use of a numerical </a><span class="Hyperlink"><a href="#1643094" title="The Nonlinear Pressure Acoustics, Time Explicit Interface">Limiter</a></span> (to capture shocks) but also the use of <span class="Hyperlink"><a href="aco_ug_ultrasound.10.45.html#1652438" title="Adaptive Mesh Refinement">Adaptive Mesh Refinement</a></span>.</div>
    <div class="Head3"><a name="1642976">Settings</a></div>
    <div class="Body_text"><a name="1642997">The </a><span class="Menu-Bodytext">Label</span> is the default physics interface name.</div>
    <div class="Body_text"><a name="1642998">The </a><span class="Menu-Bodytext">Name</span> is used primarily as a scope prefix for variables defined by the physics interface. Refer to such physics interface variables in expressions using the pattern <span class="Code">&lt;name&gt;.&lt;variable_name&gt;</span>. In order to distinguish between variables belonging to different physics interfaces, the <span class="Code">name</span> string must be unique. Only letters, numbers, and underscores (_) are permitted in the <span class="Menu-Bodytext">Name</span> field. The first character must be a letter.</div>
    <div class="Body_text"><a name="1642993">The default </a><span class="Menu-Bodytext">Name</span> (for the first physics interface in the model) is <span class="Code">nate</span>.</div>
    <div class="Head3"><a name="1643028">Filter Parameters for Absorbing Layers</a></div>
    <div class="Body_text"><a name="1643074">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_ultrasound.10.16.3.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options </span>in the <span class="Menu-Bodytext">Show More Options</span> dialog box. In the <span class="Menu-Bodytext">Filter Parameters for Absorbing Layers</span> section you can change and control the values set for the filter used in the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.39.html#1605122" title="Absorbing Layers">Absorbing Layers</a></span>. The values of the filter parameters defined here are used in all absorbing layers added to the model and they override the value of filter parameters enabled in the material model (<span class="Hyperlink"><a href="aco_ug_ultrasound.10.18.html#1643392" title="Nonlinear Pressure Acoustics, Time Explicit Model">Nonlinear Pressure Acoustics, Time Explicit Model</a></span>). The default values of the filter parameters <span class="Symbol">α</span>, <span class="Symbol">η</span><span class="EquationConstantsSubscript">c</span>, and <span class="EquationVariables">s</span> are set to 0.1, 0.01, and 2, respectively. Inside the absorbing layer it is important to use a filter that is not too aggressive since this will result in spurious reflections.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1650468"><img class="Default" src="images/aco_ug_ultrasound.10.16.4.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="1650473">For general information about the filter see the </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_equationbased.28.059.html#1149080" title="Wave Form PDE">Filter Parameters</a></span> section under <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_equationbased.28.059.html#1149030" title="Wave Form PDE">Wave Form PDE</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual.</span></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1643072">Numerical Flux</a></div>
    <div class="Body_text"><a name="1643096">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_ultrasound.10.16.5.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Stabilization </span>in the <span class="Menu-Bodytext">Show More Options</span> dialog box. Only one option exists for the <span class="Menu-Bodytext">Numerical flux</span> formulation used in the numerical scheme when solving the dG problem. For <span class="Menu-Bodytext">Lax–Friedrichs </span>(the only option) enter the <span class="Menu-Bodytext">Lax–Friedrichs flux parameter</span> <span class="Symbol">τ</span><span class="EquationConstantsSubscript">LF</span> (default is 0.2). The value of the parameter <span class="Symbol">τ</span><span class="EquationConstantsSubscript">LF </span>should be between 0 and 0.5. A parameter value of 0 represents a central flux, which is the least dissipative but also the least stable numerical flux. A parameter value of 0.5 gives a maximally dissipative global Lax–Friedrichs flux (see <span class="Hyperlink"><a href="aco_ug_ultrasound.10.49.html#1606034" title="The Lax–Friedrichs Flux">The Lax–Friedrichs Flux</a></span>).</div>
    <div class="Head3"><a name="1643094">Limiter</a></div>
    <div class="Body_text"><a name="1646600">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_ultrasound.10.16.6.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Stabilization </span>in the <span class="Menu-Bodytext">Show More Options</span> dialog box. The limiter is used to control and stabilize highly nonlinear problems with shock formation. The limiter can only be used with linear discretization. For details see the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.44.html#1647744" title="Solving Highly Nonlinear Problems">Solving Highly Nonlinear Problems</a></span> section in <span class="Hyperlink"><a href="aco_ug_ultrasound.10.43.html#1647636" title="Modeling with the Nonlinear Pressure Acoustics, Time Explicit Interface">Modeling with the Nonlinear Pressure Acoustics, Time Explicit Interface</a></span>. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1648800"><img class="Default" src="images/aco_ug_ultrasound.10.16.7.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.564651968504pt">
          <div class="Bullets-first_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-first_inner"><a name="1648802">For an example of a highly nonlinear problem that uses the WENO limiter see the </a><span class="Body_text-ital">Nonlinear Propagation of a Cylindrical Wave — Verification Model</span> tutorial model. The Application Library path <span class="Menu-Bodytext">Acoustics_Module/Nonlinear_Acoustics/ nonlinear_cylindrical_wave</span></div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-inner_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner_inner"><a name="1652741">For an example of a nonlinear problem with pulse propagation that uses mesh adaptation see the </a><span class="Body_text-ital">High-Intensity Focused Ultrasound (HIFU) Propagation Through a Tissue Phantom</span> tutorial model. The Application Library path <span class="Menu-Bodytext">Acoustics_Module/ Nonlinear_Acoustics/hifu_tissue_sample</span></div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1643109">Discretization</a></div>
    <div class="Body_text"><a name="1643133">In this section you can select the discretization for the </a><span class="Menu-Bodytext">Acoustic pressure</span> and <span class="Menu-Bodytext">Acoustic velocity</span> (the same is used for both). Per default both are set to <span class="Menu-Bodytext">Quartic</span> (4th order). Using quartic elements together with a mesh size equal to approximately one and a half of the wavelength to be resolved, leads to the best performance when using the dG method.</div>
    <div class="Head3"><a name="1643131">Dependent Variables</a></div>
    <div class="Body_text"><a name="1643158">The dependent variables are the </a><span class="Menu-Bodytext">Acoustic pressure</span>, and the<span class="Menu-Bodytext"> Acoustic velocity</span>. The names can be changed, but the names of fields and dependent variables must be unique within a model. The name for the <span class="Menu-Bodytext">Acoustic velocity</span>, <span class="Menu-Bodytext">components</span> can also be selected individually.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1649206"><img class="Default" src="images/aco_ug_ultrasound.10.16.8.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Bullets-first_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-first_inner"><a name="1649244">For information specific to modeling nonlinear problems using the time explicit interface, see </a><span class="Hyperlink"><a href="aco_ug_ultrasound.10.43.html#1647636" title="Modeling with the Nonlinear Pressure Acoustics, Time Explicit Interface">Modeling with the Nonlinear Pressure Acoustics, Time Explicit Interface</a></span>.</div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-inner_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner_inner"><a name="1649208">For information about modeling strategies, meshing, the absorbing layer and more, see the relevant information in the </a><span class="Hyperlink"><a href="aco_ug_ultrasound.10.36.html#1602988" title="Modeling with the Convected Wave Equation Interface">Modeling with the Convected Wave Equation Interface</a></span>.</div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </body>
</html>