<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Postprocessing Variables</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="973890">Postprocessing Variables</a></div>
    <div class="Body_text"><a name="1007738">This section contains information about variables for:</a></div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><span class="Hyperlink"><a href="#1008144" title="Postprocessing Variables" name="1007782">Viscous and Thermal Boundary Layer Variables</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><span class="Hyperlink"><a href="#975505" title="Postprocessing Variables" name="1007795">Material Properties</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><span class="Hyperlink"><a href="#975954" title="Postprocessing Variables" name="1008138">Stress Tensor Components</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><span class="Hyperlink"><a href="#973944" title="Postprocessing Variables" name="1008142">Power Dissipation and Intensity Variables</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><span class="Hyperlink"><a href="#1007890" title="Postprocessing Variables" name="1008149">In and Out-of-Plane Variables for the Boundary Mode Interface</a></span></div>
          </td>
        </tr>
      </table>
    </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1022741"><img class="Default" src="images/aco_ug_thermo.09.44.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 292.314642519685pt">
          <div class="Body_text-middle-cell"><a name="1022743">In the variable names, in the tables below, </a><span class="Code-ital">phys_id</span> represents the interface name. For example, <span class="Code">ta</span> for the frequency domain interface, <span class="Code">tatd</span> for the transient interface, and <span class="Code">tabm</span> for the boundary mode interface.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1008144">Viscous and Thermal Boundary Layer Variables</a></div>
    <div class="Body_text"><a name="975260">The viscous and thermal boundary layer thickness (thermal and viscous penetration depth) as defined in </a><span class="Hyperlink"><a href="aco_ug_thermo.09.46.html#1008569" title="Theory Background for the Thermoviscous Acoustics Branch">Theory Background for the Thermoviscous Acoustics Branch</a></span> can be evaluated in postprocessing for the frequency-domain models. The same is the case for the Prandtl number relating the two length scales (available in both frequency and time domain).</div>
    <table class="Basic" cellspacing="0" summary="">
      <caption>
        <div class="TableTitle">Table 6-2:  <a name="976829">Boundary Layer Variables</a></div>
      </caption>
      <tr>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 96.0001795275591pt">
          <div class="CellHeading"><a name="976833">Variable</a></div>
        </td>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 197.000362204724pt">
          <div class="CellHeading"><a name="976835">Description</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="976837">phys_id</a></span>.d_visc</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="976839">Viscous boundary layer thickness</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="976841">phys_id</a></span>.d_therm</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="976843">Thermal boundary layer thickness</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="976845">phys_id</a></span>.Pr</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="976847">Prandtl number</a></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="975505">Material Properties</a></div>
    <div class="Body_text"><a name="975530">Material properties are readily available for postprocessing in plots. In plots, click the </a><span class="Menu-Bodytext">Replace Expression</span> icon and browse to the <span class="Menu-Bodytext">Material properties</span> list under the thermoviscous acoustics interface. Important parameters to plot are the coefficient of thermal expansion <span class="Code-ital">phys_id</span><span class="Code">.alpha0</span> and the isothermal compressibility <span class="Code-ital">phys_id</span><span class="Code">.betaT</span>. These should not evaluate to zero.</div>
    <div class="Head3"><a name="975954">Stress Tensor Components</a></div>
    <div class="Body_text"><a name="975984">The stress tensor components are defined as variables and can be evaluated in postprocessing or used to create exotic couplings between physics. In the table below only the </a><span class="Code">xx</span> component of the stress tensor and the <span class="Code">x</span> component of the stress are shown. Change the spatial reference accordingly,</div>
    <table class="Basic" cellspacing="0" summary="">
      <caption>
        <div class="TableTitle">Table 6-3:  <a name="976786">Stress Variables one component in 3D</a></div>
      </caption>
      <tr>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 130.821902362205pt">
          <div class="CellHeading"><a name="976790">Variable</a></div>
        </td>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 194.857483464567pt">
          <div class="CellHeading"><a name="976792">Description</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="976794">phys_id</a></span>.K_stress_tensorxx</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="976796">Viscous stress tensor, xx-component</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="976798">phys_id</a></span>.T_stress_tensorxx</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="976800">Total stress tensor, xx-component</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="976802">phys_id</a></span>.K_stressx</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="976804">Viscous stress, x-component (on boundaries)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="976806">phys_id</a></span>.T_stressx</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="976808">Total stress, x-component (on boundaries)</a></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="973944">Power Dissipation and Intensity Variables</a></div>
    <div class="Body_text"><a name="973946">The thermoviscous acoustics interface models the energy dissipation process which stem from viscous and thermal dissipation processes. The amount of dissipated energy can be of interest as a results analysis variable or as a source term for a multiphysics problem.</a></div>
    <div class="Body_text"><a name="973952">The energy conservation-dissipation corollary describes the transport and dissipation of energy in a system (see </a><span class="Hyperlink"><a href="aco_ug_thermo.09.55.html#828357" title="References for the Thermoviscous Acoustics, Frequency Domain Interface">Ref. 1</a></span> p. 516 or <span class="Hyperlink"><a href="aco_ug_thermo.09.55.html#980048" title="References for the Thermoviscous Acoustics, Frequency Domain Interface">Ref. 6</a></span>). In linear acoustics, this equation is derived by taking the dot product (scalar product) of the momentum and the velocity <span class="EquationBold">v</span>, adding it to the continuity equation, and then adding the entropy. After some manipulation and integration, the use of the divergence theorem yields <span class="Hyperlink"><a href="#973963" title="Postprocessing Variables">Equation 6-2</a></span> </div>
    <div class="Equation"> (6-2)<a name="973963"><img class="Default" src="images/aco_ug_thermo.09.44.2.png" width="393" height="188" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="973964">where </a><span class="EquationVariables">w</span> is the disturbance energy of the control volume, <span class="EquationVariables">u = |</span><span class="EquationBold">u</span><span class="EquationVariables">|</span> is the velocity, <span class="EquationVariables">T</span> is the temperature variation, <span class="EquationVariables">p</span> is the acoustic pressure variations, <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span> is the background equilibrium pressure, <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span> the background equilibrium temperature, <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span> the background density, <span class="EquationVariables">c</span><span class="EquationConstantsSubscript">0</span> the (isentropic) speed of sound, <span class="EquationVariables">C</span><span class="EquationConstantsSubscript">p</span> the heat capacity at constant pressure (per unit mass), <span class="EquationConstants">k</span> the coefficient of thermal conduction, <span class="EquationBold">i</span> is the instantaneous intensity (flux of energy out of a control volume), <span class="Symbol_H4">Δ</span> is the dissipated energy per unit volume and time (SI unit: Pa/s = J/(m<span class="Superscript-Bodytext">3</span>s) = W/m<span class="Superscript-Bodytext">3</span>), <span class="EquationVariables">s</span> is the entropy, <span class="Symbol">τ</span> is the viscous stress tensor, <img class="Default" src="images/aco_ug_thermo.09.44.3.png" width="38" height="20" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /> is the viscous dissipation function, and <span class="Code">T</span> indicates transpose of vector. <span class="Symbol">Δ</span><span class="EquationConstantsSubscript">v</span> and <span class="Symbol">Δ</span><span class="EquationConstantsSubscript">t</span> are the viscous and thermal contributions to the dissipation function. In <span class="Hyperlink"><a href="#973963" title="Postprocessing Variables">Equation 6-2</a></span> we have made use of <span class="Hyperlink"><a href="aco_ug_thermo.09.55.html#980048" title="References for the Thermoviscous Acoustics, Frequency Domain Interface">Ref. 6</a></span> for the expression for the intensity <span class="EquationBold">I</span>.</div>
    <div class="Body_text"><a name="974004">In the Thermoviscous Acoustics, Frequency Domain interface, the dissipation term </a><span class="Symbol">Δ </span>is directly given by the RMS value of the tensor expression</div>
    <div class="Equation"> (6-3)<a name="974008"><img class="Default" src="images/aco_ug_thermo.09.44.4.png" width="234" height="36" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="974013">where</a><span class="Code"> “:”</span> in <span class="Hyperlink"><a href="#974008" title="Postprocessing Variables">Equation 6-3</a></span> is the double dot operator (or total inner product) and * is the complex conjugate. In the above expressions, the time-averaged expressions for a product in the frequency domain is defined as:</div>
    <div class="Eqn"><a name="974017"><img class="Default" src="images/aco_ug_thermo.09.44.5.png" width="314" height="36" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="973940">The power dissipation variables are defined in </a><span class="Hyperlink"><a href="#974025" title="Postprocessing Variables">Table 6-4</a></span>.</div>
    <table class="Basic" cellspacing="0" summary="">
      <caption>
        <div class="TableTitle">Table 6-4:  <a name="974025">Power dissipation Variables</a></div>
      </caption>
      <tr>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 96.0001795275591pt">
          <div class="CellHeading"><a name="974029">Variable</a></div>
        </td>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 197.000362204724pt">
          <div class="CellHeading"><a name="974031">Description</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="974033">phys_id</a></span>.diss_therm</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="974035">Thermal power dissipation density</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="974037">phys_id</a></span>.diss_visc</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="974039">Viscous power dissipation density</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="974041">phys_id</a></span>.diss_tot</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="974043">Total thermal and viscous power dissipation density</a></div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="980393">In the Thermoviscous Acoustics, Frequency Domain interface the (time averaged) intensity </a><span class="EquationBold">I </span>is given by averaging the instantaneous intensity vector <span class="EquationBold">i </span>in <span class="Hyperlink"><a href="#973963" title="Postprocessing Variables">Equation 6-2</a></span> using the same time averaged products defined above. The intensity and intensity magnitude are defined in <span class="Hyperlink"><a href="#980673" title="Postprocessing Variables">Table 6-5</a></span>.</div>
    <table class="Basic" cellspacing="0" summary="">
      <caption>
        <div class="TableTitle">Table 6-5:  <a name="980673">Intensity Variables</a></div>
      </caption>
      <tr>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 96.0001795275591pt">
          <div class="CellHeading"><a name="980677">Variable</a></div>
        </td>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 197.000362204724pt">
          <div class="CellHeading"><a name="980679">Description</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="980681">phys_id</a></span>.Ix</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="980683">Intensity x-component (in 1D, 2D, and 3D)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="980685">phys_id</a></span>.Iy</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="980687">Intensity y-component (in 2D, and 3D)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="980816">phys_id</a></span>.Iz</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="980814">Intensity z-component (2D axisymmetric and 3D)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="980818">phys_id</a></span>.Ir</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="980810">Intensity r-component (2D axisymmetric)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="980689">phys_id</a></span>.I_mag</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="980691">Intensity magnitude</a></div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="1023194">In the Thermoviscous Acoustics, Transient interface the instantaneous intensity </a><span class="EquationBold">i </span>variables is available for postprocessing. The instantaneous intensity and instantaneous intensity magnitude are defined in <span class="Hyperlink"><a href="#1023201" title="Postprocessing Variables">Table 6-6</a></span>.</div>
    <table class="Basic" cellspacing="0" summary="">
      <caption>
        <div class="TableTitle">Table 6-6:  <a name="1023201">Instantaneous Intensity Variables</a></div>
      </caption>
      <tr>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 78.3214299212598pt">
          <div class="CellHeading"><a name="1023205">Variable</a></div>
        </td>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 246.822236220472pt">
          <div class="CellHeading"><a name="1023207">Description</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="1023209">phys_id</a></span>.Iix</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="1023211">Instantaneous intensity x-component (in 1D, 2D, and 3D)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="1023213">phys_id</a></span>.Iiy</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="1023215">Instantaneous intensity y-component (in 2D, and 3D)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="1023217">phys_id</a></span>.Iiz</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="1023219">Instantaneous intensity z-component (2D axisymmetric and 3D)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="1023221">phys_id</a></span>.Iir</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="1023223">Instantaneous intensity r-component (2D axisymmetric)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBodymono"><span class="Code-ital"><a name="1023225">phys_id</a></span>.Ii_mag</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="1023227">Instantaneous intensity magnitude</a></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1007890">In and Out-of-Plane Variables for the Boundary Mode Interface</a></div>
    <div class="Body_text"><a name="1007961">Several dedicated variables exist for </a><span class="Hyperlink"><a href="aco_ug_thermo.09.33.html#986235" title="The Thermoviscous Acoustics, Boundary Mode Interface">The Thermoviscous Acoustics, Boundary Mode Interface</a></span> where quantities are defined in terms of their in-plane and out-of-plane values. For example, the intensity variable <span class="EquationBold">I</span> has the following derived values</div>
    <div class="Eqn"><a name="1008566"><img class="Default" src="images/aco_ug_thermo.09.44.6.png" width="111" height="48" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1008567">where </a><span class="EquationConstants">ip</span> stands for in-plane and <span class="EquationConstants">op</span> for out-of-plane. These two variables are named <span class="Code">tabm.Iip</span> and <span class="Code">tabm.Iop</span> (with spatial components <span class="Code">x</span>, <span class="Code">y</span>, and <span class="Code">z</span>). The magnitude of these two variables is given by <span class="Code">tabm.Iip_mag</span> and <span class="Code">tabm.Iop_mag</span>. In the same manner variables exist for the acceleration and the velocity.</div>
  </body>
</html>