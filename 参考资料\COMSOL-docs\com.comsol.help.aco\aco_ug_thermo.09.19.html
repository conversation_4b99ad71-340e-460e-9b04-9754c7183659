<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Slip</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="816697">Slip</a></div>
    <div class="Body_text"><a name="818141">Use the </a><span class="Menu-Bodytext">Slip </span>node to prescribe zero normal velocity, and therefore also zero tangential stress, condition on the boundary. Add this node from the <span class="Menu-Bodytext">Mechanical </span>submenu. The condition reads</div>
    <div class="Eqn"><a name="828635"><img class="Default" src="images/aco_ug_thermo.09.19.1.png" width="332" height="91" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1015283">This results in a so-called no-penetration condition where no viscous boundary layer is created. Use the condition in places where the viscous losses in the boundary layer are nonimportant. Here it is not necessary to mesh the boundary layer resulting in fewer mesh elements.</a></div>
    <div class="Body_text"><a name="1030545">When running a model with a perfectly matched layer (PML), it is recommended not to combine a </a><span class="Menu-Bodytext">Slip</span> condition with a rational scaling in the PML, as this will lead to a singularity and an error.</div>
    <div class="Body_text"><a name="1015284">Per default, the </a><span class="Menu-Bodytext">Slip</span> condition uses a so-called discontinuous Galerkin or penalty formulation to prevent locking problems on curved surfaces.</div>
    <div class="Head3"><a name="1015285">Constraint Settings</a></div>
    <div class="Body_text"><a name="1015289">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.19.2.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box.</div>
    <div class="Body_text"><a name="1043436">Select </a><span class="Menu-Bodytext">Use weak constraints</span> if you want to switch from the default discontinuous Galerkin (penalty like) formulation to a Lagrange multiplier formulation for the slip condition (this formulation is not suited for an iterative solver). The Lagrange multiplier formulation also prevents locking problems.</div>
    <div class="Head3"><a name="1047246">Excluded Edges/Points</a></div>
    <div class="Body_text"><a name="1047236">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.19.3.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. See <span class="Hyperlink"><a href="aco_ug_thermo.09.45.html#1039488" title="Suppressing Constraints on Lower Dimensions">Suppressing Constraints on Lower Dimensions</a></span> for details.</div>
  </body>
</html>