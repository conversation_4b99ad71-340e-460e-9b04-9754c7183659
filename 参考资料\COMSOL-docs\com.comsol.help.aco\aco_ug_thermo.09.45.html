<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Suppressing Constraints on Lower Dimensions</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1039488">Suppressing Constraints on Lower Dimensions</a></div>
    <div class="Body_text"><a name="1038279">Sometimes, boundary conditions on two adjacent objects can come into conflict on a shared object (point or edge). For the boundary conditions in the thermoviscous physics that are based on constraints, you have the possibility to select that certain objects of lower dimensions should be excluded from the main selection. To do this, you must first click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.45.1.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. In the settings, a new section named <span class="Menu-Bodytext">Excluded Edges</span> and/or <span class="Menu-Bodytext">Excluded Points</span> will then appear (when applicable). In these sections, you can select geometrical objects which should be excluded from the main selection when the constraint is applied.</div>
    <div class="Head4"><a name="1047590">Velocity Condition</a></div>
    <div class="Body_text"><a name="1047627">When using the mechanical </a><span class="Hyperlink"><a href="aco_ug_thermo.09.20.html#860345" title="Velocity">Velocity</a></span> condition to you may get conflicts if it is located next to a no-slip condition (a wall). To remedy this, you can exclude the common edge or point.</div>
    <div class="Head4"><a name="1047431">Periodic Condition</a></div>
    <div class="Body_text"><a name="1038275">When using the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.13.html#1037074" title="Periodic Condition">Periodic Condition</a></span>, you may get conflicts or duplicates which makes the model either overconstrained or problematic for the automatic constraint elimination algorithm. If you are aware of such situations, it is good practice to remove one of the potentially conflicting constraints. This could be in multiphysics models where you combine solid mechanics with thermoviscous acoustics and have edges where you both apply a periodic condition (solid and thermoviscous acoustics) and the Thermoviscous Acoustic-Structure Boundary multiphysics coupling.</div>
    <div class="Body_text"><a name="1038303">Particularly if the geometry is curved, there is a risk that these constraints are not identical from a numerical point of view. In this case, excluding the conflicting edge from the selected boundary will make the behavior unique and fully predictable.</a></div>
    <div class="Body_text"><a name="1039735">Another example where constraints will come in conflict is if you want to, for example, use periodic condition on parts of the geometry using weak constraints, while keeping the default pointwise constraints on other parts. If the same mesh node has both types of constraints, the solution will fail, so you must exclude any common geometrical objects from the selection in one of the constraints.</a></div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1040757"><img class="Default" src="images/aco_ug_thermo.09.45.2.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.00214488189pt">
          <div class="Body_text-middle-cell"><a name="1040762">See also </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_modeling.15.65.html#1241643" title="Excluded Surfaces, Excluded Edges, and Excluded Points">Excluded Surfaces, Excluded Edges, and Excluded Points</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span>.</div>
        </td>
      </tr>
    </table>
  </body>
</html>