<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>The Convected Wave Equation, Time Explicit Interface</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head1"><a name="1447802">The Convected Wave Equation, Time Explicit Interface</a></div>
    <div class="Body_text"><a name="1447807">The </a><span class="Menu-Bodytext">Convected Wave Equation, Time Explicit (cwe) </span>interface (<img class="Default" src="images/aco_ug_ultrasound.10.02.1.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />), found under the <span class="Menu-Bodytext">Acoustics&gt;Ultrasound</span> subbranch (<img class="Default" src="images/aco_ug_ultrasound.10.02.2.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) when adding a physics interface, is used to solve large transient linear acoustic problems containing many wavelengths in a stationary background flow. It is suited for time-dependent simulations with arbitrary time-dependent sources and fields. In general, the interface is suited for modeling the propagation of acoustic signals over large distances relative to the wavelength, for example, linear ultrasound problems. Application areas include ultrasound flowmeters and other ultrasound sensors where time of flight is an important parameter. The applications are not restricted to ultrasound. The interface includes absorbing layers that are used to set up effective nonreflecting like boundary conditions. The interface exists in 2D, 2D axisymmetric, and 3D.</div>
    <div class="Body_text"><a name="1605564">The interface is based on the discontinuous Galerkin (dG or dG-FEM) method and uses a time explicit solver. The method is very memory efficient and can solve problems with many million degrees of freedom (DOFs). The method is also well suited for distributed computing on clusters.</a></div>
    <div class="Body_text"><a name="1637013">In the no-flow case it can be advantageous to use </a><span class="Hyperlink"><a href="aco_ug_pressure.05.086.html#1581128" title="The Pressure Acoustics, Time Explicit Interface">The Pressure Acoustics, Time Explicit Interface</a></span> instead. The equations are the same but the latter interface has more options to, for example, model scattering phenomena using a scattered field formulation; or for handling sharp material discontinuities.</div>
    <div class="Body_text"><a name="1605626">The interface solves the linearized Euler equations assuming an adiabatic equation of state. The dependent variables are the acoustic pressure </a><span class="EquationVariables">p</span> and the acoustic velocity perturbation <span class="EquationBold">u</span>. The background mean flow can be any stationary flow with small to moderate velocity gradients. General bulk dissipation (volumetric damping) can be added to model real fluids. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.5413291338583pt">
          <div class="img-tables"><a name="1634580"><img class="Default" src="images/aco_ug_ultrasound.10.02.3.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.031681889764pt">
          <div class="Body_text-middle-cell"><a name="1634582">When solving models that are based on the dG method, optimizing the mesh is important. For further details see </a><span class="Hyperlink"><a href="aco_ug_ultrasound.10.37.html#1633183" title="Meshing, Discretization, and Solvers">Optimizing the Mesh for DG</a></span> in the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.37.html#1616772" title="Meshing, Discretization, and Solvers">Meshing, Discretization, and Solvers</a></span> section.</div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="1605616">When this physics interface is added, these default nodes are also added to the </a><span class="Menu-Bodytext">Model Builder</span> — <span class="Menu-Bodytext">Convected Wave Equation Model</span>, <span class="Menu-Bodytext">Sound Hard Wall</span>, <span class="Menu-Bodytext">Initial Values</span>, and <span class="Menu-Bodytext">Axial Symmetry</span> (in case of 2D axisymmetric). Then, from the <span class="Menu-Bodytext">Physics</span> toolbar, add other nodes that implement, for example, boundary conditions. You can also right-click <span class="Menu-Bodytext">Convected Wave Equation, Time Explicit </span>to select physics features from the context menu.</div>
    <div class="Head3"><a name="1449362">Settings</a></div>
    <div class="Body_text"><a name="1449363">The </a><span class="Menu-Bodytext">Label</span> is the default physics interface name. </div>
    <div class="Body_text"><a name="1449364">The </a><span class="Menu-Bodytext">Name</span> is used primarily as a scope prefix for variables defined by the physics interface. Refer to such physics interface variables in expressions using the pattern <span class="Code">&lt;name&gt;.&lt;variable_name&gt;</span>. In order to distinguish between variables belonging to different physics interfaces, the <span class="Code">name</span> string must be unique. Only letters, numbers, and underscores (_) are permitted in the <span class="Menu-Bodytext">Name</span> field. The first character must be a letter.</div>
    <div class="Body_text"><a name="1605349">The default </a><span class="Menu-Bodytext">Name</span> (for the first physics interface in the model) is <span class="Code">cwe</span>.</div>
    <div class="Head3"><a name="1605371">Filter Parameters for Absorbing Layers</a></div>
    <div class="Body_text"><a name="1605449">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_ultrasound.10.02.4.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. In the <span class="Menu-Bodytext">Filter Parameters for Absorbing Layers</span> section you can change and control the values set for the filter used in the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.39.html#1605122" title="Absorbing Layers">Absorbing Layers</a></span>. The values of the filter parameters defined here are used in all absorbing layers added to the model and they override the value of filter parameters enabled in the material model (<span class="Hyperlink"><a href="aco_ug_ultrasound.10.04.html#1603060" title="Convected Wave Equation Model">Convected Wave Equation Model</a></span>). The default values of the filter parameters <span class="Symbol">α</span>, <span class="Symbol">η</span><span class="EquationConstantsSubscript">c</span>, and <span class="EquationVariables">s</span> are set to 0.1, 0.01, and 2, respectively. Inside the absorbing layer it is important to use a filter that is not too aggressive since this will result in spurious reflections.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1607156"><img class="Default" src="images/aco_ug_ultrasound.10.02.5.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="1607158">For general information about the filter see the </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_equationbased.28.059.html#1149080" title="Wave Form PDE">Filter Parameters</a></span> section under <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_equationbased.28.059.html#1149030" title="Wave Form PDE">Wave Form PDE</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual.</span></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1631023">Equation settings (2D Axisymmetric only)</a></div>
    <div class="Body_text"><a name="1631078">This section is only available in </a><span class="Menu-Bodytext">2D Axisymmetric</span>. Per default the acoustic velocity perturbation <span class="EquationBold">u</span> is assumed to have only two components: the radial component <span class="EquationVariables">u</span><span class="EquationVariablesSubscript">r</span> and the axial component <span class="EquationVariables">u</span><span class="EquationVariablesSubscript">z</span>. You can select the <span class="Menu-Bodytext">Include out-of-plane components</span> check box to enable the third — circumferential component <img class="Default" src="images/aco_ug_ultrasound.10.02.6.png" width="21" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />, which physically can only be nonzero if the background mean flow has the corresponding component.</div>
    <div class="Head3"><a name="1605421">Discretization</a></div>
    <div class="Body_text"><a name="1605456">In this section you can select the discretization for the </a><span class="Menu-Bodytext">Acoustic pressure</span> and <span class="Menu-Bodytext">Acoustic velocity</span>. Per default both are set to <span class="Menu-Bodytext">Quartic</span> (4th order). Using quartic elements together with a mesh size equal to approximately half the wavelength to be resolved, leads to the best performance when using the dG method. For further details see the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.37.html#1616772" title="Meshing, Discretization, and Solvers">Meshing, Discretization, and Solvers</a></span> section.</div>
    <div class="Head3"><a name="1605350">Dependent Variables</a></div>
    <div class="Body_text"><a name="1605351">The dependent variables (field variables) are the </a><span class="Menu-Bodytext">Acoustic pressure</span>, <span class="Menu-Bodytext">Acoustic velocity</span>, and <span class="Menu-Bodytext">Acoustic velocity</span>, <span class="Menu-Bodytext">components</span>. The names can be changed, but the names of fields and dependent variables must be unique within a model.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1605357"><img class="Default" src="images/aco_ug_ultrasound.10.02.7.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Bullets-first_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-first_inner"><a name="1605362">For information about modeling strategies, meshing, the absorbing layer and more, see </a><span class="Hyperlink"><a href="aco_ug_ultrasound.10.36.html#1602988" title="Modeling with the Convected Wave Equation Interface">Modeling with the Convected Wave Equation Interface</a></span>.</div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-inner_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner_inner"><a name="1606115">For the theoretical background of the model solved, see </a><span class="Hyperlink"><a href="aco_ug_ultrasound.10.46.html#1430317" title="Theory for the Convected Wave Equation Interface">Theory for the Convected Wave Equation Interface</a></span>.</div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1630570"><img class="Default" src="images/aco_ug_ultrasound.10.02.8.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.564651968504pt">
          <div class="Bullets-first_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-first_inner"><span class="Body_text-ital"><a name="1630574">Gaussian Pulse in 2D Uniform Flow: Convected Wave Equation and Absorbing Layers</a></span>. The Application Library path: <span class="Menu-Bodytext">Acoustics_Module/Tutorials,_Pressure_Acoustics/<br />gaussian_pulse_absorbing_layers</span></div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-inner_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner_inner"><span class="Body_text-ital"><a name="1630603">Ultrasound Flowmeter with Generic Time-of-Flight Configuration</a></span><span class="Menu-Bodytext">. </span>The Application Library path: <span class="Menu-Bodytext">Acoustics_Module/Ultrasound/ultrasound_flow_meter_generic</span></div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </body>
</html>