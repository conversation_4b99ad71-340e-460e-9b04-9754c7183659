<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>General Flux/Source</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1644084">General Flux/Source</a></div>
    <div class="Body_text"><a name="1644113">The </a><span class="Menu-Bodytext">General Flux/Source</span> node, found under the <span class="Menu-Bodytext">More </span>submenu, adds the most general boundary condition for the Nonlinear Pressure Acoustics, Time Explicit model, as it is formulated (in the conservative form) for the discontinuous Galerkin method (dG). The condition defines the normal pressure and velocity fluxes <span class="EquationVariables">g</span><span class="EquationVariablesSubscript">p</span> and <span class="EquationBold">g</span><span class="EquationVariablesSubscript">u</span> at an exterior boundary, given by</div>
    <div class="Eqn"><a name="1644117"><img class="Default" src="images/aco_ug_ultrasound.10.34.1.png" width="201" height="67" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1644118">where </a><span class="EquationBold">u</span> = (<span class="EquationVariables">u</span>,<span class="EquationVariables"> v</span>, <span class="EquationVariables">w</span>) and <span class="EquationBold">n</span> = (n<span class="EquationVariablesSubscript">x</span>, n<span class="EquationVariablesSubscript">y</span>, n<span class="EquationVariablesSubscript">z</span>) in 3D and 2D, or <span class="EquationBold">n</span> = (n<span class="EquationVariablesSubscript">r</span>, n<span class="Subscript-Symbol">ϕ</span>, n<span class="EquationVariablesSubscript">z</span>) in 2D axisymmetric. Note that this condition is expressed in terms of the dependent variables and not the total fields. Care should be taken when using this advanced condition as the method is sensitive to proper formulation of boundary conditions. Only one characteristic can enter the domain at any boundary at the time, meaning that it is easy to overspecify the problem. Use the mesh normals (<span class="Code">nxmesh</span>,<span class="Code"> nymesh</span>, and <span class="Code">nzmesh</span>;<span class="Code"> </span>or <span class="Code">nrmesh</span>,<span class="Code"> nphimesh</span>, and <span class="Code">nzmesh</span>) in the expression you define.</div>
    <div class="Head3"><a name="1644119">General Flux/Source</a></div>
    <div class="Body_text"><a name="1644109">Enter the expression for the components of the </a><span class="Menu-Bodytext">Flux vector </span><span class="EquationVariables">g</span><span class="EquationVariablesSubscript">p</span><span class="Menu-Bodytext"> and </span><span class="EquationBold">g</span><span class="EquationVariablesSubscript">u</span>. </div>
  </body>
</html>