<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Sound Hard Wall</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1637448">Sound Hard Wall</a></div>
    <div class="Body_text"><a name="1637451">The </a><span class="Menu-Bodytext">Sound Hard Wall</span> adds a boundary condition for a sound hard boundary or wall, which is a boundary at which the normal component of the velocity is zero (a slip condition). This is assumed true for both the acoustic velocity and the background velocity</div>
    <div class="Eqn"><a name="1637439"><img class="Default" src="images/aco_ug_ultrasound.10.06.1.png" width="181" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1637443">Note that this condition is not identical to the </a><span class="Hyperlink"><a href="aco_ug_ultrasound.10.10.html#1597996" title="Symmetry">Symmetry</a></span> condition where only the normal component of the acoustic velocity is assumed to be zero.</div>
  </body>
</html>