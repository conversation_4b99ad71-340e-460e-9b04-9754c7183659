<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Vibroacoustic Applications</title>
    <link rel="StyleSheet" href="css/aco_ug_sme.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="2408835">Vibroacoustic Applications</a></div>
    <div class="Body_text"><a name="2403488">The Solid Mechanics and Piezoelectricity interfaces are available with the Acoustics Module such that vibroacoustic problems involving solids, piezoelectric materials, and acoustic domains can be modeled. These multiphysics applications include piezo transducers, loudspeaker drivers, loudspeaker cabinets, automotive applications, and sound transmission/isolation problems. Pure elastic wave problems can also be modeled as the Solid Mechanics equations are formulated include the full dynamic behavior.</a></div>
    <div class="Body_text"><a name="2404199">Additional multiphysics capabilities of the Acoustics Module are enabled when it is combined with the Structural Mechanics Module. The Shell interface, which is part of the Structural Mechanics Module, enables predefined acoustic-shell couplings. The Acoustic-Shell and Thermoacoustic-Shell interaction multiphysics interfaces are enabled. The option to couple to shells, membranes and multibody dynamics using the Acoustic-Structure Boundary is also enabled, see the </a><span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.01.html#548506" title="Multiphysics Couplings">Multiphysics Couplings</a></span> chapter for further details.</div>
  </body>
</html>