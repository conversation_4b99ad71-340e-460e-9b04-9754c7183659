<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>The Thermoviscous Acoustics, Frequency Domain Interface</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head1"><a name="954726">The Thermoviscous Acoustics, Frequency Domain Interface</a></div>
    <div class="Body_text"><a name="954706">The </a><span class="Menu-Bodytext">Thermoviscous Acoustics, Frequency Domain (ta) </span>interface (<img class="Default" src="images/aco_ug_thermo.09.02.01.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />), found under the <span class="Menu-Bodytext">Thermoviscous Acoustics </span>branch (<img class="Default" src="images/aco_ug_thermo.09.02.02.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) when adding a physics interface, is used to compute the acoustic variations of pressure, velocity, and temperature. This physics interface is required to accurately model acoustics in geometries of small dimensions, often referred to as <span class="Body_text-ital">microacoustics</span>. Near walls, viscous losses and thermal conduction become important because a boundary layers exists. The thicknesses of these boundary layers are also known as the viscous and thermal penetration depth. For this reason, it is necessary to include thermal conduction effects and viscous losses explicitly in the governing equations. It is, for example, used when modeling the response of transducers like microphones, miniature loudspeakers, and receivers. Other applications include analyzing feedback in hearing aids and in mobile devices, or studying the damped vibrations of MEMS structures.</div>
    <div class="Body_text"><a name="935577">The physics interface solves the equations in the frequency domain assuming all fields and sources to be harmonic. The harmonic variation of all fields and sources is given by </a><span class="EquationVariables">e</span><span class="EquationVariablesSuperscript">i</span><span class="Superscript-Symbol">ω</span><span class="EquationVariablesSuperscript">t</span> using the <span class="Symbol">+</span><span class="EquationVariables">i</span><span class="Symbol">ω</span> convention. Linear acoustics is assumed. Nonlinear effects can be included when modeling in the time domain using <span class="Hyperlink"><a href="aco_ug_thermo.09.28.html#1016762" title="The Thermoviscous Acoustics, Transient Interface">The Thermoviscous Acoustics, Transient Interface</a></span> and the <span class="Hyperlink"><a href="aco_ug_thermo.09.31.html#1058860" title="Nonlinear Thermoviscous Acoustics Contributions">Nonlinear Thermoviscous Acoustics Contributions</a></span> feature.</div>
    <div class="Body_text"><a name="935579">The equations defined by the </a><span class="Body_text-ital">Thermoviscous Acoustics, Frequency Domain</span> interface are the linearized Navier–Stokes equations in quiescent background conditions solving the continuity, momentum, and energy equations (first order perturbation equations). Thermoviscous acoustics is also known as <span class="Body_text-ital">viscothermal acoustics</span> or sometimes <span class="Body_text-ital">thermoacoustics</span> (not to be confused with the field discussing heating and cooling using acoustics). Due to the detailed description necessary when modeling thermoviscous acoustics, the model simultaneously solves for the acoustic pressure <span class="EquationVariables">p</span>, the acoustic velocity variation <span class="EquationBold">u</span> (particle velocity), and the acoustic temperature variations <span class="EquationVariables">T</span>. It is available for 3D, 2D, and 1D Cartesian geometries as well as for 2D and 1D axisymmetric geometries.</div>
    <div class="Body_text"><a name="988435">The </a><span class="Body_text-ital">Thermoviscous Acoustics, Frequency Domain</span> interface is formulated in the so-called scattered field formulation where the total field (subscript <span class="EquationConstants">t</span>) is the sum of the scattered field (the field solved for, <span class="EquationVariables">p</span>,<span class="EquationVariables"> </span><span class="EquationBold">u</span>, and <span class="EquationVariables">T</span>) and a possible background acoustic field (subscript <span class="EquationConstants">b</span>), such that</div>
    <div class="Eqn"><a name="988668"><img class="Default" src="images/aco_ug_thermo.09.02.03.png" width="301" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="988739">When no </a><span class="Hyperlink"><a href="aco_ug_thermo.09.05.html#988838" title="Background Acoustic Fields">Background Acoustic Fields</a></span> feature is present (the background field values are zero per default) the total field is simply the field solved for, <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span> = <span class="EquationVariables">p</span>, <span class="EquationBold">u</span><span class="EquationConstantsSubscript">t</span> = <span class="EquationBold">u</span>, and <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">t</span> = <span class="EquationVariables">T</span>. All governing equations and boundary conditions are formulated in the total field variables.</div>
    <div class="Body_text"><a name="989577">When this physics interface is added, these default nodes are also added to the </a><span class="Menu-Bodytext">Model Builder</span> — <span class="Menu-Bodytext">Thermoviscous Acoustics Model</span>, <span class="Menu-Bodytext">Wall</span>, and <span class="Menu-Bodytext">Initial Values</span>. Then, from the <span class="Menu-Bodytext">Physics</span> toolbar, add other nodes that implement, for example, boundary conditions and sources. You can also right-click <span class="Menu-Bodytext">Thermoviscous Acoustics </span>to select physics features from the context menu.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="989671"><img class="Default" src="images/aco_ug_thermo.09.02.04.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.283398425197pt">
          <div class="Body_text-middle-cell"><a name="989673">For good modeling strategies, solver suggestions, postprocessing information, as well as tips and tricks, see the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.39.html#973323" title="Modeling with the Thermoviscous Acoustics Branch">Modeling with the Thermoviscous Acoustics Branch</a></span> section.</div>
        </td>
      </tr>
    </table>
    <div class="Head4"><a name="989578">On the Thermoviscous Acoustics Physics Interface</a></div>
    <div class="Body_text"><a name="829478">The T</a><span class="Body_text-ital">hermoviscous Acoustics, Frequency Domain</span> interface solves, as mentioned, the full linearized Navier–Stokes (momentum), continuity, and energy equations. It solves for the propagation of compressible linear waves in a general viscous and thermally conductive fluid. The length scale at which the thermoviscous acoustic description is necessary is given by the thickness of the viscous boundary layer (the viscous penetration depth), which is</div>
    <div class="Eqn"><a name="828450"><img class="Default" src="images/aco_ug_thermo.09.02.05.png" width="78" height="44" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="911910">and the thickness of the thermal boundary layer (the thermal penetration depth)</a></div>
    <div class="Eqn"><a name="913964"><img class="Default" src="images/aco_ug_thermo.09.02.06.png" width="101" height="44" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1011664">where the definition of the symbols </a><span class="EquationVariables">f</span>, <span class="Symbol">μ</span>, <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span>, <span class="EquationConstants">k</span>, and <span class="EquationVariables">C</span><span class="EquationConstantsSubscript">p</span> may be found in <span class="Hyperlink"><a href="#914125" title="The Thermoviscous Acoustics, Frequency Domain Interface">Table 6-1</a></span>. The thickness of both boundary layers depends on the frequency  <span class="EquationVariables">f</span> and decreases with increasing frequency. The ratio of the two length scales is related to the nondimensional Prandtl number <span class="EquationConstants">Pr</span>, by</div>
    <div class="Eqn"><a name="828483"><img class="Default" src="images/aco_ug_thermo.09.02.07.png" width="126" height="47" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="822026">which define the relative importance of the thermal and viscous effects for a given material. In air at 20 </a><span class="EquationConstantsSuperscript">o</span>C and 1 atm, the viscous boundary layer thickness is 0.22 mm at 100 Hz while it is only 55 <span class="Symbol">μ</span>m in water under the same conditions. The Prandtl number is 0.7 in air and 7 in water.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="913597"><img class="Default" src="images/aco_ug_thermo.09.02.08.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 292.314642519685pt">
          <div class="Body_text-middle-cell"><a name="913599">Evaluate the value of the viscous and thermal boundary layer thickness as well as the Prandtl number in postprocessing. They are defined by the variables </a><span class="Code">ta.d_visc</span>, <span class="Code">ta.d_therm</span>, and <span class="Code">ta.Pr</span>, respectively.</div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="913585">The physical quantities commonly used in the thermoviscous acoustics interfaces are defined in </a><span class="Hyperlink"><a href="#914125" title="The Thermoviscous Acoustics, Frequency Domain Interface">Table 6-1</a></span> below.</div>
    <table class="Basic" cellspacing="0" summary="">
      <caption>
        <div class="TableTitle">Table 6-1:  <a name="914125">Thermoviscous acoustics, Frequency Domain Physical Quantities.</a></div>
      </caption>
      <tr>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 122.192588976378pt">
          <div class="CellHeading"><a name="914133">Quantity</a></div>
        </td>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 58.1849291338583pt">
          <div class="CellHeading"><a name="914135">Symbol</a></div>
        </td>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 86.2994267716536pt">
          <div class="CellHeading"><a name="914137">SI Unit</a></div>
        </td>
        <td style="background-color: WhiteSmoke; border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top; width: 60.3787464566929pt">
          <div class="CellHeading"><a name="914139">Abbreviation</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914141">Pressure (acoustic)</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914143">  </a><span class="EquationVariables">p</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914145">pascal</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914147">Pa</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988912">Total acoustic pressure</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988914"> </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">t</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988916">pascal</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988918">Pa</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988936">Scattered acoustic pressure</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988938"> </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">s</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988940">pascal</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988942">Pa</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914149">Temperature variation (acoustic)</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914151"> </a><span class="EquationVariables">T</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914153">kelvin</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914155">K</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988924">Total temperature variation</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988926"> </a><span class="EquationVariables">T</span><span class="EquationConstantsSubscript">t</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988928">kelvin</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988930">K</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988956">Scattered temperature variation</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988958"> </a><span class="EquationVariables">T</span><span class="EquationConstantsSubscript">s</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988960">kelvin</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988962">K</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914157">Acoustic velocity field</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914159"> </a><span class="EquationBold">u </span><span class="EquationVariables">= </span><span class="Symbol">(</span><span class="EquationVariables">u</span>, <span class="EquationVariables">v</span>, <span class="EquationVariables">w</span><span class="Symbol">)</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914161">meter/second</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914163">m/s</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988980">Total acoustic velocity field</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988982"> </a><span class="EquationBold">u</span><span class="EquationConstantsSubscript">t</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988984">meter/second</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988986">m/s</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988972">Scattered acoustic velocity field</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988974"> </a><span class="EquationBold">u</span><span class="EquationConstantsSubscript">s</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988976">meter/second</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="988978">m/s</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914165">Dynamic viscosity</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914167"> </a><span class="Symbol">μ</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><span class="CellBody"><a name="914169">pascal-second</a></span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914171">Pa·s</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914173">Bulk viscosity</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914175"> </a><span class="Symbol">μ</span><span class="EquationConstantsSubscript">B</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><span class="CellBody"><a name="914177">pascal-second</a></span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914179">Pa·s</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914181">Thermal conductivity</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914183"> </a><span class="EquationConstants">k</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914185">watt/meter-kelvin</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914187">W/(m·K)</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914189">Heat capacity at constant pressure</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914191"> </a><span class="EquationVariables">C</span><span class="EquationConstantsSubscript">p</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914193">joule/meter</a><span class="Superscript-CellBody">3</span>-kelvin</div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914195">J/(m</a><span class="Superscript-CellBody">3</span>·K)</div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><span class="CellBody"><a name="914197">Isothermal compressibility</a></span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914199"> </a><span class="Symbol">β</span><span class="EquationConstantsSubscript">T</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914201">1/pascal</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914203">1/Pa</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914205">Coefficient of thermal expansion (isobaric)</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914207"> </a><span class="Symbol">α</span><span class="EquationConstantsSubscript">p</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914209">1/kelvin</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914211">1/K</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914213">Ratio of specific heats</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914215"> </a><span class="Symbol">γ</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914217">(dimensionless)</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914219">1</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914221">Frequency</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914223">  </a><span class="EquationVariables">f</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914225">hertz</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914227">Hz</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914229">Wave number</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><span class="EquationVariables"><a name="914231"> </a></span> <span class="EquationVariables">k</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914233">1/meter</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914235">1/m</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914237">Equilibrium pressure</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914239"> </a><span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914241">pascal</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914243">Pa</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914245">Equilibrium density</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914247"> </a><span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914249">kilogram/meter</a><span class="Superscript-CellBody">3</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914251">kg/m</a><span class="Superscript-CellBody">3</span></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914253">Equilibrium temperature</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914255"> </a><span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914257">kelvin</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914259">K</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914261">Speed of sound</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914263"> </a><span class="EquationVariables">c</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914265">meter/second</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914267">m/s</a></div>
        </td>
      </tr>
      <tr>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914269">Acoustic impedance</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914271"> </a><span class="EquationVariables">Z</span></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914273">pascal-second/meter</a></div>
        </td>
        <td style="border-bottom-color: Silver; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Silver; border-right-style: solid; border-right-width: 1px; border-top-color: Silver; border-top-style: solid; border-top-width: 1px; padding-bottom: 2pt; padding-left: 2pt; padding-right: 2pt; padding-top: 2pt; vertical-align: top">
          <div class="CellBody"><a name="914275">Pa·s/m</a></div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="945369"><img class="Default" src="images/aco_ug_thermo.09.02.09.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.00214488189pt">
          <div class="Body_text-middle-cell"><a name="945371">As the thermoviscous acoustics physics interface solves for pressure, velocity, and temperature, models can easily become large and contain many DOFs. See </a><span class="Hyperlink"><a href="aco_ug_thermo.09.41.html#973409" title="Solver Suggestions for Large Thermoviscous Acoustics Models">Solver Suggestions for Large Thermoviscous Acoustics Models</a></span> for suggestions on how to solve large thermoviscous acoustic models.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="958795">Settings</a></div>
    <div class="Body_text"><a name="958796">The </a><span class="Menu-Bodytext">Label</span> is the default physics interface name. </div>
    <div class="Body_text"><a name="958797">The </a><span class="Menu-Bodytext">Name</span> is used primarily as a scope prefix for variables defined by the physics interface. Refer to such physics interface variables in expressions using the pattern <span class="Code">&lt;name&gt;.&lt;variable_name&gt;</span>. In order to distinguish between variables belonging to different physics interfaces, the <span class="Code">name</span> string must be unique. Only letters, numbers, and underscores (_) are permitted in the <span class="Menu-Bodytext">Name</span> field. The first character must be a letter.</div>
    <div class="Body_text"><a name="808949">The default </a><span class="Menu-Bodytext">Name</span> (for the first physics interface in the model) is <span class="Code">ta</span>.</div>
    <div class="Head3"><a name="984153">Equation</a></div>
    <div class="Body_text"><a name="984154">Expand the </a><span class="Menu-Bodytext">Equation</span> section to see the equations solved for with the <span class="Menu-Bodytext">Equation form</span> specified. The default selection for <span class="Menu-Bodytext">Equation form</span> is set to <span class="Menu-Bodytext">Study controlled</span>. The available studies are selected under <span class="Menu-Bodytext">Show equations assuming</span>.</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="984155">For </a><span class="Menu-Bodytext">Study controlled</span>, the scaling of the equations is optimized for the numerical performance of the different solvers and study types.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1058255">For </a><span class="Menu-Bodytext">Frequency domain</span> you can manually enter the scaling parameter <span class="Symbol">Δ</span> under the <span class="Menu-Bodytext">Thermoviscous Acoustics Equation Settings</span> section.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="984215">For </a><span class="Menu-Bodytext">Mode analysis </span>you can manually enter the <span class="Menu-Bodytext">Mode analysis frequency </span>(applicable in 2D and 2D axisymmetry).</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Head3"><a name="984434">Thermoviscous Acoustics Equation Settings</a></div>
    <div class="Head4"><a name="1081751">Adiabatic Formulation</a></div>
    <div class="Body_text"><a name="1034327">Click to select </a><span class="Menu-Bodytext">Adiabatic formulation</span> to use an adiabatic equation of state and disable the temperature degree of freedom for the thermoviscous acoustic equations. This formulation is applicable when the thermal losses can be disregarded, this is often the case in liquids, like water. In gases, like air, on the other hand the full formulation is necessary. When <span class="Menu-Bodytext">Adiabatic formulation</span> is selected all temperature conditions and options are disabled in the user interface.</div>
    <div class="Head4"><a name="1081805">Out-of-Plane Mode Extension</a></div>
    <div class="Body_text"><a name="1081839">In 2D and 2D axisymmetric models, click to select </a><span class="Menu-Bodytext">Out-of-plane mode extension</span> in order to model the third dimension implicitly. When selected, an additional degree of freedom, for the velocity, is solved for in the out-of-plane direction. The behavior is determined by entering the <span class="Menu-Bodytext">Out-of-plane wave number</span> <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">z</span> in 2D models; or by entering the <span class="Menu-Bodytext">Azimuthal mode number</span> <span class="EquationVariables">m</span> in 2D axisymmetric models. The latter defines an azimuthal wave number <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">m</span> = <span class="EquationVariables">m</span>/<span class="EquationVariables">r</span>. All out-of-plane gradients of the fields are then defined in terms of the wave number. The solved equations closely follow the ones used in the <span class="Hyperlink"><a href="aco_ug_thermo.09.53.html#999820" title="Formulation for the Boundary Mode Interface">Formulation for the Boundary Mode Interface</a></span>.</div>
    <div class="Head4"><a name="1081748">Scaling Factor</a></div>
    <div class="Body_text"><a name="984589">For all component dimensions, and if required, click to expand the </a><span class="Menu-Bodytext">Equation</span> section, then select <span class="Menu-Bodytext">Frequency domain</span> as the <span class="Menu-Bodytext">Equation form</span> and enter the settings as described below.</div>
    <div class="Body_text"><a name="984491">The default </a><span class="Menu-Bodytext">Scaling factor</span> <span class="Symbol">Δ </span>is 1/(<span class="EquationVariables">i</span><span class="Symbol">ω)</span>. This value corresponds to the equations for a <span class="Body_text-ital">Frequency Domain</span> study when the equations are study controlled. To get the equations corresponding to an <span class="Body_text-ital">Eigenfrequency</span> study, change the <span class="Menu-Bodytext">Scaling factor</span> <span class="Symbol">Δ</span> to 1. Changing the scaling factor influences the coupling to other physics.</div>
    <div class="Head3"><a name="865901">Sound Pressure Level Settings</a></div>
    <div class="Body_text"><a name="908439">See the settings for </a><span class="Hyperlink"><a href="aco_ug_pressure.05.002.html#1388618" title="The Pressure Acoustics, Frequency Domain Interface">Sound Pressure Level Settings</a></span> for the <span class="Body_text-ital">Pressure Acoustics, Frequency Domain</span> interface.</div>
    <div class="Head3"><a name="865903">Typical Wave Speed for Perfectly Matched Layers</a></div>
    <div class="Body_text"><a name="865904">Enter a value or expression for the typical wave speed for perfectly matched layers </a><span class="EquationVariables">c</span><span class="EquationConstantsSubscript">ref</span> (SI unit: m/s). The default is 343 m/s.</div>
    <div class="Head3"><a name="1069334">Stabilization</a></div>
    <div class="Body_text"><a name="1069353">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.02.10.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Stabilization</span>. Select <span class="Menu-Bodytext">No stabilization applied</span> (the default), <span class="Menu-Bodytext">Galerkin least-squares (GLS) stabilization</span>, or <span class="Menu-Bodytext">Streamline upwind Petrov-Galerkin (SUPG) stabilization</span>. When linear thermoviscous acoustic problems are solved (like in the frequency domain) the numerical problem is stable with the default P1-P2-P2 discretization. Enabling stabilization will ensure stability also for other combinations of discretization orders.</div>
    <div class="Head3"><a name="1048021">Global Port Settings</a></div>
    <div class="Body_text"><a name="1048054">Select to enable the </a><span class="Menu-Bodytext">Activate port sweep</span> option (not selected per default). This option is used to compute the full scattering matrix when <span class="Hyperlink"><a href="aco_ug_thermo.09.12.html#1042563" title="Port">Port</a></span> conditions are used. For more details see <span class="Hyperlink"><a href="aco_ug_thermo.09.12.html#1048532" title="Port">The Port Sweep Functionality</a></span> subsection. The section only exists for 3D, 2D, and 2D axisymmetry.</div>
    <div class="Body_text"><a name="1062622">Select the </a><span class="Menu-Bodytext">Mode shape normalization</span> as <span class="Menu-Bodytext">Amplitude normalized</span> (the default) or <span class="Menu-Bodytext">Power normalized</span>. This setting controls if the mode shapes are normalized to have a unit maximum pressure amplitude or carry unit power. The selection determines how the scattering matrix is to be interpreted.</div>
    <div class="Head3"><a name="810634">Discretization</a></div>
    <div class="Body_text"><a name="1020734">From the list select the element order and type (Lagrange or serendipity) for the </a><span class="Menu-Bodytext">Pressure</span>, the <span class="Menu-Bodytext">Velocity field</span>, and the <span class="Menu-Bodytext">Temperature variation</span>, respectively. The default is <span class="Menu-Bodytext">Linear </span>for the pressure and <span class="Menu-Bodytext">Quadratic serendipity</span> for the velocity and the temperature.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1020766"><img class="Default" src="images/aco_ug_thermo.09.02.11.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Bullets-first_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-first_inner"><a name="1020768">For numerical stability reasons, the element order for the pressure should be one less than the element order for the velocity. Unless </a><span class="Hyperlink"><a href="#1069334" title="The Thermoviscous Acoustics, Frequency Domain Interface">Stabilization</a></span> is enabled, in which case, for example, a P1-P1-P1 discretization can be used.</div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-inner_outer" style="margin-left: 6pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner_inner"><a name="1020769">In fluids where the thermal and viscous boundary layer thickness are of the same order of magnitude (where the Prandtl number </a><span class="EquationConstants">Pr</span> is of the order 1, like in air), it is recommended to use the same element order for the temperature and the velocity. Both fields vary equally over the same length scale in the acoustic boundary layers near walls.</div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1020775"><img class="Default" src="images/aco_ug_thermo.09.02.12.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.283398425197pt">
          <div class="Body_text-middle-cell"><a name="1020780">Choosing between </a><span class="Hyperlink"><a href="aco_ug_thermo.09.42.html#1019545" title="Lagrange and Serendipity Shape Functions">Lagrange and Serendipity Shape Functions</a></span> has influence on the number of DOFs solved for and on stability for distorted meshes.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="986916">Dependent Variables</a></div>
    <div class="Body_text"><a name="986891">This physics interface defines these dependent variables (fields): the </a><span class="Menu-Bodytext">Pressure</span> <span class="EquationVariables">p</span>, the <span class="Menu-Bodytext">Velocity field</span> <span class="EquationBold">u</span> and its components, and the <span class="Menu-Bodytext">Temperature variation</span> <span class="EquationVariables">T</span>. The names can be changed but the names of fields and dependent variables must be unique within a model.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="876646"><img class="Default" src="images/aco_ug_thermo.09.02.13.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Bullets-inner-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner-cell_inner"><span class="Hyperlink"><a href="aco_ug_thermo.09.03.html#811519" title="Domain, Boundary, and Pair Nodes for the Thermoviscous Acoustics, Frequency Domain Interface" name="876654">Domain, Boundary, and Pair Nodes for the Thermoviscous Acoustics, Frequency Domain Interface</a></span></div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-last-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-last-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-last-cell_inner"><span class="Hyperlink"><a href="aco_ug_thermo.09.46.html#1008569" title="Theory Background for the Thermoviscous Acoustics Branch" name="876659">Theory Background for the Thermoviscous Acoustics Branch</a></span></div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="972804"><img class="Default" src="images/aco_ug_thermo.09.02.14.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.845905511811pt">
          <div class="Body_text-middle-cell"><a name="972809">In the </a><span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span> see <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_environment.14.38.html#1445913" title="Common Physics Interface and Feature Settings and Nodes">Table 2-4</a></span> for links to common sections and <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_environment.14.38.html#1446125" title="Common Physics Interface and Feature Settings and Nodes">Table 2-5</a></span> to common feature nodes. You can also search for information: press F1 to open the <span class="Menu-Bodytext">Help</span> window or Ctrl+F1 to open the <span class="Menu-Bodytext">Documentation</span> window.</div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="892301"><img class="Default" src="images/aco_ug_thermo.09.02.15.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.564651968504pt">
          <div class="Bullets-first-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-first-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-first-cell_inner"><span class="Body_text-ital"><a name="892307">Uniform Layer Waveguide</a></span>: Application Library path <span class="Menu-Bodytext">Acoustics_Module/Verification_Examples/uniform_layer_waveguide</span></div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-inner-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner-cell_inner"><span class="Body_text-ital"><a name="1029673">Transfer Impedance of a Perforate</a></span><span class="Menu-Bodytext">: </span>Application Library path <span class="Menu-Bodytext">Acoustics_Module/Tutorials,_Thermoviscous_Acoustics/transfer_impedance_perforate</span></div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-last-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-last-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-last-cell_inner"><span class="Body_text-ital"><a name="892311">Generic 711 Coupler — An Occluded Ear-Canal Simulator</a></span>: Application Library path <span class="Menu-Bodytext">Acoustics_Module/Tutorials,_Thermoviscous_Acoustics/generic_711_coupler</span></div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </body>
</html>