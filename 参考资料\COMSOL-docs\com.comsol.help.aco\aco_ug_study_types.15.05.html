<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Eigenfrequency Study</title>
    <link rel="StyleSheet" href="css/aco_ug_study_types.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1253985">Eigenfrequency Study</a></div>
    <div class="Body_text"><a name="1253986">If all sources are removed from a frequency-domain equation, its solution becomes zero for all but a discrete set of angular frequencies </a><span class="Symbol">ω</span>, where the solution has a well-defined shape but undefined magnitude. These solutions are known as eigenmodes and their corresponding frequencies as eigenfrequencies.</div>
    <div class="Body_text"><a name="1253989">The eigenmodes and eigenfrequencies have many interesting mathematical properties, but also direct physical significance because they identify the </a><span class="Body_text-ital">resonance frequency</span> (or frequencies) of the structure. When approaching a resonance frequency in a harmonically-driven problem, a weaker and weaker source is needed to maintain a given response level. At the actual eigenfrequency, the time-harmonic problem loses the uniqueness of the solution for a nonzero excitation.</div>
    <div class="Body_text"><a name="1253994">Select the </a><span class="Menu-Bodytext">Eigenfrequency</span> study type (<img class="Default" src="images/aco_ug_study_types.15.05.1.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) when you are interested in the resonance frequencies of the acoustic domain or the structure, whether you want to exploit them, as in a musical instrument, or avoid them, as in a reactive muffler or inside a hi-fi speaker system. To an engineer, the distribution of eigenfrequencies and the shape of eigenmodes can also give a good first impression about the behavior of a system.</div>
    <div class="Body_text"><a name="1253995">An eigenfrequency analysis solves for the eigenfrequencies and the shape of the eigenmodes. When performing an eigenfrequency analysis, specify whether to look at the mathematically more fundamental eigenvalue </a><span class="Symbol">λ</span> (available as the variable <span class="Code">lambda</span>) or the eigenfrequency <span class="EquationVariables">f</span>, which is more commonly used in an acoustics context:</div>
    <div class="Eqn"><a name="1254010"><img class="Default" src="images/aco_ug_study_types.15.05.2.png" width="54" height="36" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /> </a></div>
    <div class="Head3"><a name="1362667">Nonlinear eigenfrequency analysis</a></div>
    <div class="Body_text"><a name="1362759">In certain circumstances the material properties or boundary conditions can be frequency dependent. This is, for example, the case if a model set up with </a><span class="Hyperlink"><a href="aco_ug_pressure.05.002.html#630838" title="The Pressure Acoustics, Frequency Domain Interface">The Pressure Acoustics, Frequency Domain Interface</a></span> contains a <span class="Hyperlink"><a href="aco_ug_pressure.05.006.html#1270537" title="Narrow Region Acoustics">Narrow Region Acoustics</a></span> or a <span class="Hyperlink"><a href="aco_ug_pressure.05.005.html#1226466" title="Poroacoustics">Poroacoustics</a></span> equivalent fluid model. The same is true if the <span class="Hyperlink"><a href="aco_ug_pressure.05.021.html#1388987" title="Impedance">Impedance</a></span> boundary condition is added: most of the options listed in <span class="Hyperlink"><a href="aco_ug_pressure.05.158.html#1395067" title="Theory for the Boundary Impedance Models">Theory for the Boundary Impedance Models</a></span> define the acoustic impedance as a function of frequency. If the frequency dependence in these models is nonlinear, this will lead to a so-called nonlinear eigenvalue problem, which is more complex and therefore often must be treated carefully.</div>
    <div class="Body_text"><a name="1363650">While solving a nonlinear eigenvalue problem, the linearization around some linearization point takes place behind the scenes. Expand the </a><span class="Menu-Bodytext">Study</span> node, right-click the <span class="Menu-Bodytext">Solver Configurations</span> node, and select <span class="Menu-Bodytext">Show Default Solver</span>. In the generated solver sequence, navigate to the <span class="Menu-Bodytext">Eigenvalue Solver</span> node and find the <span class="Menu-Bodytext">Transform point</span> field. The value specified there will be used as the linearization point in the eigenvalue study, and therefore it is very important to set up for nonlinear eigenvalue problems.</div>
    <div class="Body_text"><a name="1364536">The default </a><span class="Menu-Bodytext">Transform point</span> value used in the acoustics interfaces is 100 Hz. If the eigenfrequencies of interest lie in kHz or MHz range, the default value of the linearization point may not ensure an accurate solution of a nonlinear eigenvalue problem — depending on its nonlinear properties. In this case, it is good practice to move the linearization point closer to the range of desired eigenfrequencies. For example, it can be the mean of the range. The other option is to define the <span class="Menu-Bodytext">Transform point</span> as a parameter, for example <span class="Code">TP</span>, add a <span class="Menu-Bodytext">Parametric Sweep </span>over this parameter to the study, and search for one eigenfrequency around <span class="Code">TP</span>. This approach will be more accurate if the model exhibits strong nonlinear behavior within the range of desired eigenfrequencies, where a constant linearization point in not enough. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1254004"><img class="Default" src="images/aco_ug_study_types.15.05.3.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.283398425197pt">
          <div class="Body_text-middle-cell"><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_solver.32.023.html#1432480" title="Eigenfrequency" name="1254009">Eigenfrequency</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span></div>
        </td>
      </tr>
    </table>
  </body>
</html>