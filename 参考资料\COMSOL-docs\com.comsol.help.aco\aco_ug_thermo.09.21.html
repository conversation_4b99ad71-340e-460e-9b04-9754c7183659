<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>No Stress</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="816711">No Stress</a></div>
    <div class="Body_text"><a name="818172">Use the </a><span class="Menu-Bodytext">No Stress </span>node to set the total surface stress equal to zero. Add this node from the <span class="Menu-Bodytext">Mechanical </span>submenu. The condition reads</div>
    <div class="Eqn"><a name="828809"><img class="Default" src="images/aco_ug_thermo.09.21.1.png" width="316" height="42" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
  </body>
</html>