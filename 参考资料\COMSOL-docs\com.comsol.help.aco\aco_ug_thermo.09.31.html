<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Nonlinear Thermoviscous Acoustics Contributions</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1058860">Nonlinear Thermoviscous Acoustics Contributions</a></div>
    <div class="Body_text"><a name="1058917">The </a><span class="Menu-Bodytext">Nonlinear Thermoviscous Acoustics Contributions</span> feature adds the necessary contributions to the governing <span class="Hyperlink"><a href="aco_ug_thermo.09.30.html#1075981" title="Thermoviscous Acoustics Model">Equation 6-1</a></span> in order to model nonlinear effects in a transient thermoviscous simulation. The contributions allow modeling of vortex shedding that may happen at sudden expansions, like in a perforate, a grill, or at a miniature sound port. Vortex shedding will in general introduce distortion in the measured response of a system, with the generation of harmonics. The feature can also capture the nonlinear effect associated with high sound pressure levels that require a nonlinear representation of the equation of state (pressure, density, and temperature relation). The nonlinear contributions to the left-hand side of <span class="Hyperlink"><a href="aco_ug_thermo.09.30.html#1075981" title="Thermoviscous Acoustics Model">Equation 6-1</a></span> are:</div>
    <div class="Eqn"><a name="1065365"><img class="Default" src="images/aco_ug_thermo.09.31.1.png" width="462" height="152" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
    <div class="Body_text"><a name="1059666">These represent all the second order nonlinear contributions, of the acoustic perturbation fields, to the governing equations (keeping a linear equation of state). This includes the nonlinear convective terms that are necessary to model vortex shedding and other similar effects with detachment. The contributions are important for modeling local high particle velocity situations, when the linearity condition |</a><span class="EquationBold">u</span><span class="EquationConstantsSubscript">t</span>| &lt;&lt; <span class="EquationVariables">c</span> is no longer fulfilled. The contributions to the energy equation are not added when the <span class="Menu-Bodytext">Adiabatic formulation</span> is used. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="1064613"><img class="Default" src="images/aco_ug_thermo.09.31.2.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.564651968504pt">
          <div class="Body_text"><span class="Body_text-ital"><a name="1064618">Nonlinear Slit Resonator</a></span>. Application Library path: <span class="Menu-Bodytext">Acoustics_Module/Nonlinear_Acoustics/nonlinear_slit_resonator</span></div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="1065924">For systems where the linearity condition on the density </a><span class="Symbol">ρ</span><span class="EquationConstantsSubscript">t</span> &lt;&lt; <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span> is no longer fulfilled, the density expansion can be changed to include second order terms (see below). This will allow modeling of so-called cumulative effects.</div>
    <div class="Body_text"><a name="1068121">The </a><span class="Menu-Bodytext">Nonlinear Thermoviscous Acoustics Contributions</span> feature is not compatible with the <span class="Hyperlink"><a href="aco_ug_thermo.09.32.html#1021330" title="Background Acoustic Fields">Background Acoustic Fields</a></span> feature. The superposition principle is not valid in a nonlinear model.</div>
    <div class="Body_text"><a name="1068145">Note that when solving nonlinear models, it is often necessary to use numerical stabilization. Turn it on in the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.28.html#1067381" title="The Thermoviscous Acoustics, Transient Interface">Stabilization</a></span> section. Per default no stabilization is used. Using stabilization also allows to switch to a P1-P1-P1 discretization which can be more efficient in transient models. Remember to use an adequate mesh for a lower order discretization, especially in the acoustic boundary layers.</div>
    <div class="Head3"><a name="1066042">Model Inputs</a></div>
    <div class="Body_text"><a name="1066270">The model inputs for the </a><span class="Menu-Bodytext">Equilibrium pressure</span> <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span> and the <span class="Menu-Bodytext">Equilibrium temperature</span> <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span> are always visible as they contribute to the governing equations.</div>
    <div class="Head3"><a name="1066057">Density Representation</a></div>
    <div class="Body_text"><a name="1066473">In models with high local sound pressure levels, the linear equation of state may no longer be valid. This happens when the linearity condition on the density </a><span class="Symbol">ρ</span><span class="EquationConstantsSubscript">t</span> &lt;&lt; <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span> is no longer fulfilled. If necessary, change the default <span class="Menu-Bodytext">Density expansion</span> from <span class="Menu-Bodytext">First order</span> to <span class="Menu-Bodytext">Second order</span>.</div>
    <div class="Body_text"><a name="1066827">When </a><span class="Menu-Bodytext">Second order</span> is selected, additional inputs to the model are necessary. For the general case, the second order derivatives of the equilibrium density <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span> = <span class="Symbol">ρ</span><span class="EquationConstantsSubscript">0</span>(<span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0</span>,<span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span>) with respect to pressure <span class="EquationVariables">p</span><span class="EquationConstantsSubscript">0 </span>an temperature are necessary <span class="EquationVariables">T</span><span class="EquationConstantsSubscript">0</span> are needed. They contribute to the second-order Taylor expansion of the density. Per default, they are taken <span class="Menu-Bodytext">From equilibrium density</span>; this implies that the dependency of the density on pressure and temperature should be correct.</div>
    <div class="Body_text"><a name="1067281">If the </a><span class="Menu-Bodytext">Adiabatic</span> formulation is used (see <span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#984434" title="The Thermoviscous Acoustics, Frequency Domain Interface">Thermoviscous Acoustics Equation Settings</a></span>), the user interface inputs correspond to the <span class="Hyperlink"><a href="aco_ug_pressure.05.055.html#1666591" title="Nonlinear Acoustics (Westervelt) Contributions">Nonlinear Acoustics (Westervelt) Contributions</a></span> equation in Pressure Acoustics, Transient. Select to specify the <span class="Menu-Bodytext">Parameter of nonlinearity</span> (default), the <span class="Menu-Bodytext">Ratio of specific heats (for gases)</span>, or the <span class="Menu-Bodytext">Coefficient of nonlinearity</span>.</div>
    <div class="Head3"><a name="1066081">Viscous Heating</a></div>
    <div class="Body_text"><a name="1064603">Click to select </a><span class="Menu-Bodytext">Include viscous dissipation</span> (disabled per default). This will add a right-hand side heat source to the energy equation. The viscous dissipation is a nonlinear (second order) effect and can only be included in the nonlinear model.</div>
  </body>
</html>