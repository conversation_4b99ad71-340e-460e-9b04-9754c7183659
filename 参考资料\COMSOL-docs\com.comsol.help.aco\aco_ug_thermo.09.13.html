<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Periodic Condition</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1037074">Periodic Condition</a></div>
    <div class="Body_text"><a name="1037175">The </a><span class="Menu-Bodytext">Periodic Condition</span> node adds a periodic boundary condition that can be used to reduce the model size by using symmetries and periodicities in the geometry and physics interfaces being modeled. This feature works well for cases like opposing parallel boundaries. In other cases, use a <span class="Menu-Bodytext">Destination Selection</span> subnode to control the destination. By default, it contains the source and destination selection that COMSOL Multiphysics identifies. The periodic condition automatically defines a mapping between the source <span class="EquationBold">x</span><span class="EquationConstantsSubscript">s</span> and the destination <span class="EquationBold">x</span><span class="EquationConstantsSubscript">d </span>points on the boundaries.</div>
    <div class="Head3"><a name="1037176">Periodicity Settings</a></div>
    <div class="Body_text"><a name="1037177">Select a </a><span class="Menu-Bodytext">Type of periodicity</span>: <span class="Menu-Bodytext">Continuity</span> (the default), <span class="Menu-Bodytext">Antiperiodicity</span>, <span class="Menu-Bodytext">Floquet periodicity</span> (Bloch periodicity), <span class="Menu-Bodytext">Cyclic symmetry</span>, or <span class="Menu-Bodytext">User Defined</span>.</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1038516">For </a><span class="Menu-Bodytext">Continuity</span> the values of the field variables at destination are set equal to the source: <span class="EquationVariables">p</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">d</span>) = <span class="EquationVariables">p</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">s</span>), <span class="EquationBold">u</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">d</span>) = <span class="EquationBold">u</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">s</span>), and <span class="EquationVariables">T</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">d</span>) = <span class="EquationVariables">T</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">s</span>). If the source and destination boundaries are rotated with respect to each other, a transformation is automatically performed, so that corresponding velocity components are connected.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1039071">For Antiperiodicity the values of the field variables on the destination are set equal to the values on the source with the sign reversed: </a><span class="EquationVariables">p</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">d</span>) = -<span class="EquationVariables">p</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">s</span>), <span class="EquationBold">u</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">d</span>) = -<span class="EquationBold">u</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">s</span>), and <span class="EquationVariables">T</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">d</span>) = -<span class="EquationVariables">T</span>(<span class="EquationBold">x</span><span class="EquationConstantsSubscript">s</span>). If the source and destination boundaries are rotated with respect to each other, a transformation is automatically performed, so that corresponding velocity components are connected.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1037178">For </a><span class="Menu-Bodytext">Floquet periodicity</span>, also known as Bloch periodicity, enter a <span class="Menu-Bodytext">k-vector for Floquet periodicity</span> <span class="EquationBold">k</span><span class="EquationConstantsSubscript">F</span> (SI unit: rad/m) for the <span class="Menu-Bodytext">x</span>, <span class="Menu-Bodytext">y</span>, and <span class="Menu-Bodytext">z</span> coordinates (3D components), the <span class="Menu-Bodytext">r</span> and <span class="Menu-Bodytext">z</span> coordinates (2D axisymmetric components), or <span class="Menu-Bodytext">x</span> and <span class="Menu-Bodytext">y</span> coordinates (2D components). This is the wave number of the excitation.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-textcontinued"><a name="1037179">This condition is used to model infinite periodic structures with nonnormal incident pressure fields or excitations. Use it to model, for example, a large perforated plate with an oblique incident wave with wave vector </a><span class="EquationBold">k</span> (and set <span class="EquationBold">k</span><span class="EquationBoldSubscript">F</span> = <span class="EquationBold">k</span>) by only analyzing one hole or one subset of holes that is periodic.</div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1037180">For </a><span class="Menu-Bodytext">Cyclic symmetry</span> select a <span class="Menu-Bodytext">Sector angle</span>: <span class="Menu-Bodytext">Automatic</span> (the default) or <span class="Menu-Bodytext">User defined</span>. For <span class="Menu-Bodytext">User defined</span> enter a value for <span class="Symbol">θ</span><span class="EquationConstantsSubscript">S </span>(SI unit: rad). Enter an <span class="Menu-Bodytext">Azimuthal</span> <span class="Menu-Bodytext">mode number</span> <span class="EquationVariables">m</span> (dimensionless). </div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-textcontinued"><a name="1037181">This condition is used to model any geometry that has a cyclic periodic structure such as a microphone or a loudspeaker driver. Setting the azimuthal mode number determines what mode is analyzed. The response of the full system to an external excitation is in general a linear combination of many different modes.</a></div>
    <div class="Bullets-inner_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-inner_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-inner_inner"><a name="1039272">For </a><span class="Menu-Bodytext">User defined</span> select the check box for any of the field variables as needed. Then for each selection, choose the <span class="Menu-Bodytext">Type of periodicity</span> — <span class="Menu-Bodytext">Continuity</span> or <span class="Menu-Bodytext">Antiperiodicity</span>. If the source and destination boundaries are rotated with respect to each other, a transformation is automatically performed, so that corresponding velocity components are connected.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Body_text"><a name="1037182">In the time domain, both the </a><span class="Menu-Bodytext">Cyclic symmetry</span> and the <span class="Menu-Bodytext">Floquet periodicity</span> boundary conditions reduce to the continuity condition.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1037188"><img class="Default" src="images/aco_ug_thermo.09.13.1.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.845905511811pt">
          <div class="Body_text-middle-cell"><a name="1037190">To optimize the performance of the </a><span class="Menu-Bodytext">Floquet periodicity</span> and the <span class="Menu-Bodytext">Cyclic symmetry</span> conditions, it is recommended that the source and destination meshes are identical. This can be achieved by first meshing the source boundary or edge and then copying the mesh to the destination boundary or edge. When the <span class="Menu-Bodytext">Periodic Condition</span> stretches across regions with a mix of default material models, PMLs, background pressure fields, or background acoustic fields, it is recommended to add one <span class="Menu-Bodytext">Periodic Condition</span> for each set of such boundaries.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="1046584">Constraint Settings</a></div>
    <div class="Body_text"><a name="1046588">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.13.2.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box.</div>
    <div class="Head3"><a name="1046589">Excluded Edges/Points</a></div>
    <div class="Body_text"><a name="1046593">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.13.3.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. See <span class="Hyperlink"><a href="aco_ug_thermo.09.45.html#1039488" title="Suppressing Constraints on Lower Dimensions">Suppressing Constraints on Lower Dimensions</a></span> for details.</div>
    <div class="Head3"><a name="1037196">Orientation of Source</a></div>
    <div class="Body_text"><a name="1037405">To display this section, click the </a><span class="Menu-Bodytext">Show More Options</span> button (<img class="Default" src="images/aco_ug_thermo.09.13.4.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) and select <span class="Menu-Bodytext">Advanced Physics Options</span> in the <span class="Menu-Bodytext">Show More Options</span> dialog box. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1038160"><img class="Default" src="images/aco_ug_thermo.09.13.5.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Body_text"><a name="1040004">See </a><span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_modeling.15.67.html#1222382" title="Periodic Boundary Conditions">Orientation of Source and Destination</a></span> in the <span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span>.</div>
        </td>
      </tr>
    </table>
  </body>
</html>