<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>The Thermoviscous Acoustics, Boundary Mode Interface</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head1"><a name="986235">The Thermoviscous Acoustics, Boundary Mode Interface</a></div>
    <div class="Body_text"><a name="986240">The </a><span class="Menu-Bodytext">Thermoviscous Acoustics, Boundary Mode (tabm) </span>interface (<img class="Default" src="images/aco_ug_thermo.09.33.1.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />), found under the <span class="Menu-Bodytext">Thermoviscous Acoustics </span>branch (<img class="Default" src="images/aco_ug_thermo.09.33.2.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) when adding a physics interface, is used to compute and identify propagating and nonpropagating modes in waveguides and ducts. The interface performs a boundary mode analysis on a given boundary, including the thermal and viscous loss effects that are important in the acoustic boundary layer near walls.</div>
    <div class="Body_text"><a name="993949">The interface is applied at boundaries which represent the cross section or the inlet of a waveguide or duct of small dimensions. It solves for the acoustic variations of pressure </a><span class="EquationVariables">p</span>, velocity <span class="EquationBold">u</span>, and temperature <span class="EquationVariables">T</span>, as well as the out-of-plane wave number <span class="EquationVariables">k</span><span class="EquationConstantsSubscript">n</span> of the modes. Near walls, viscous losses and thermal conduction become important because boundary layers exists. The thickness of these layers is known as the viscous and thermal penetration depth. For this reason, it is necessary to include thermal conduction effects and viscous losses explicitly in the governing equations. The Thermoviscous Acoustics, Boundary Mode interface is, for example, used when setting up sources in systems with small ducts like hearing aids or mobile devices. It can also be used to identify the propagating wave number and characteristic impedance of a duct cross section and use that information in the homogenized <span class="Hyperlink"><a href="aco_ug_pressure.05.006.html#1270537" title="Narrow Region Acoustics">Narrow Region Acoustics</a></span> model in <span class="Hyperlink"><a href="aco_ug_pressure.05.002.html#630838" title="The Pressure Acoustics, Frequency Domain Interface">The Pressure Acoustics, Frequency Domain Interface</a></span>.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="1033852"><img class="Default" src="images/aco_ug_thermo.09.33.3.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.00214488189pt">
          <div class="Body_text-middle-cell"><a name="1033854">The propagation wave number is defined by the postprocessing variable </a><span class="Code">tabm.kn</span> and the (lumped) characteristic impedance by the variable <span class="Code">tabm.Zc</span>. Both are global variables.</div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="994292">The Thermoviscous Acoustics, Boundary Mode interface solves the equations defined by the linearized Navier-Stokes equations (linearized continuity, momentum, and energy equations), in quiescent background conditions, on boundaries, searching for the out-of-plane wave numbers at a given frequency. All gradients in the governing equations are expressed in terms of the in-plane gradient and the out-of-plane wave number that is being solved for. Due to the detailed description necessary when modeling thermoviscous acoustics, the model simultaneously solves for the acoustic pressure </a><span class="EquationVariables">p</span>, the velocity variation <span class="EquationBold">u</span> (particle velocity), and the acoustic temperature variations <span class="EquationVariables">T</span>. The interface is available on boundaries for 3D and on edges for 2D axisymmetric geometries.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="994611"><img class="Default" src="images/aco_ug_thermo.09.33.4.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Body_text"><a name="994620">For details about the governing equations, see the </a><span class="Hyperlink"><a href="aco_ug_thermo.09.46.html#1008569" title="Theory Background for the Thermoviscous Acoustics Branch">Theory Background for the Thermoviscous Acoustics Branch</a></span>.</div>
        </td>
      </tr>
    </table>
    <div class="Body_text"><a name="985131">When this physics interface is added, these default nodes are also added to the </a><span class="Menu-Bodytext">Model Builder</span> — <span class="Menu-Bodytext">Thermoviscous Acoustics Model</span>, <span class="Menu-Bodytext">Wall</span>, and <span class="Menu-Bodytext">Initial Values</span>. Then, from the <span class="Menu-Bodytext">Physics</span> toolbar, add other nodes that implement, for example, boundary conditions and sources. You can also right-click <span class="Menu-Bodytext">Thermoviscous Acoustics, Boundary Mode </span>to select physics features from the context menu.</div>
    <div class="Head3"><a name="985327">Settings</a></div>
    <div class="Body_text"><a name="985328">The </a><span class="Menu-Bodytext">Label</span> is the default physics interface name.</div>
    <div class="Body_text"><a name="985329">The </a><span class="Menu-Bodytext">Name</span> is used primarily as a scope prefix for variables defined by the physics interface. Refer to such physics interface variables in expressions using the pattern <span class="Code">&lt;name&gt;.&lt;variable_name&gt;</span>. In order to distinguish between variables belonging to different physics interfaces, the <span class="Code">name</span> string must be unique. Only letters, numbers, and underscores (_) are permitted in the <span class="Menu-Bodytext">Name</span> field. The first character must be a letter.</div>
    <div class="Body_text"><a name="985330">The default </a><span class="Menu-Bodytext">Name</span> (for the first physics interface in the model) is <span class="Code">tabm</span>.</div>
    <div class="Head3"><a name="985331">Equation</a></div>
    <div class="Body_text"><a name="985332">Expand the </a><span class="Menu-Bodytext">Equation</span> section to see the equations solved for with the <span class="Menu-Bodytext">Equation form</span> specified. The default selection is <span class="Menu-Bodytext">Equation form</span> is set to <span class="Menu-Bodytext">Study controlled</span>. The available studies are selected under <span class="Menu-Bodytext">Show equations assuming</span>.</div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="985333">For </a><span class="Menu-Bodytext">Study controlled</span>, the frequency used for the mode analysis study is given in the study.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="985334">For </a><span class="Menu-Bodytext">Mode analysis </span>you can set the frequency manually. The default <span class="Menu-Bodytext">Mode analysis frequency</span> <span class="EquationVariables">f</span> is 100 Hz.</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Head3"><a name="985338">Sound Pressure Level Settings</a></div>
    <div class="Body_text"><a name="985342">See the settings for </a><span class="Hyperlink"><a href="aco_ug_pressure.05.002.html#1388618" title="The Pressure Acoustics, Frequency Domain Interface">Sound Pressure Level Settings</a></span> for the Pressure Acoustics, Frequency Domain interface.</div>
    <div class="Head3"><a name="985348">Discretization</a></div>
    <div class="Body_text"><a name="985352">From the list, select the element order for the </a><span class="Menu-Bodytext">Pressure</span>, the <span class="Menu-Bodytext">Velocity field</span>, and the <span class="Menu-Bodytext">Temperature variation</span>. The default uses <span class="Menu-Bodytext">Linear</span> elements for the pressure and <span class="Menu-Bodytext">Quadratic serendipity </span>for the velocity field and the temperature variations.</div>
    <div class="Body_text"><a name="1006447">In order for the system to be numerically stable, it is important that the order for the pressure degree of freedom (DOF) is one lower than the velocity field. Per default, the velocity components and the temperature share the same element order as they vary similarly over the same length scale in the acoustic boundary layer. Therefore, both typically require the same spatial accuracy. There is no stabilization option for the boundary mode interface. </a></div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="985360"><img class="Default" src="images/aco_ug_thermo.09.33.5.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Body_text-cell"><a name="1005844">Increase the element order to, for example, quadratic for the pressure and cubic for the velocity and temperature dofs. This can add additional accuracy but it also adds additional degrees of freedom compared to the default setting and will be computationally more expensive.</a></div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="986771">Dependent Variables</a></div>
    <div class="Body_text"><a name="986760">This physics interface defines these dependent variables (fields), the </a><span class="Menu-Bodytext">Pressure</span> <span class="EquationVariables">p</span>, the <span class="Menu-Bodytext">Velocity field</span> <span class="EquationBold">u</span> and its components, and the <span class="Menu-Bodytext">Temperature variation</span> <span class="EquationVariables">T</span>. The names can be changed but the names of fields and dependent variables must be unique within a model.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="985368"><img class="Default" src="images/aco_ug_thermo.09.33.6.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Bullets-inner-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner-cell_inner"><span class="Hyperlink"><a href="aco_ug_thermo.09.03.html#811519" title="Domain, Boundary, and Pair Nodes for the Thermoviscous Acoustics, Frequency Domain Interface" name="985372">Domain, Boundary, and Pair Nodes for the Thermoviscous Acoustics, Frequency Domain Interface</a></span></div>
                </td>
              </tr>
            </table>
          </div>
          <div class="Bullets-last-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-last-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-last-cell_inner"><span class="Hyperlink"><a href="aco_ug_thermo.09.46.html#1008569" title="Theory Background for the Thermoviscous Acoustics Branch" name="985377">Theory Background for the Thermoviscous Acoustics Branch</a></span></div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="985383"><img class="Default" src="images/aco_ug_thermo.09.33.7.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 291.845905511811pt">
          <div class="Body_text-middle-cell"><a name="985388">In the </a><span class="Body_text-ital">COMSOL Multiphysics Reference Manual</span> see <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_environment.14.38.html#1445913" title="Common Physics Interface and Feature Settings and Nodes">Table 2-4</a></span> for links to common sections and <span class="Hyperlink"><a href="../com.comsol.help.comsol/comsol_ref_environment.14.38.html#1446125" title="Common Physics Interface and Feature Settings and Nodes">Table 2-5</a></span> to common feature nodes. You can also search for information: press F1 to open the <span class="Menu-Bodytext">Help</span> window or Ctrl+F1 to open the <span class="Menu-Bodytext">Documentation</span> window.</div>
        </td>
      </tr>
    </table>
  </body>
</html>