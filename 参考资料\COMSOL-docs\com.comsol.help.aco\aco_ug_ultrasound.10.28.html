<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>Normal Velocity</title>
    <link rel="StyleSheet" href="css/aco_ug_ultrasound.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head2"><a name="1643843">Normal Velocity</a></div>
    <div class="Body_text"><a name="1643921">The </a><span class="Menu-Bodytext">Normal Velocity</span> condition adds a source specified in terms of an inward normal velocity <span class="EquationVariables">v</span><span class="EquationConstantsSubscript">n</span>(<span class="EquationVariables">t</span>) or velocity <span class="EquationBold">v</span><span class="EquationConstantsSubscript">0</span>(<span class="EquationVariables">t</span>) of the boundary. The component in the normal direction is used to define the boundary condition. This feature represents an external source term like a vibrating surface. The condition is also available from the <span class="Menu-Bodytext">Pairs</span> submenu, as an option at interfaces between parts in an assembly. This effectively introduces a condition similar to the <span class="Hyperlink"><a href="aco_ug_ultrasound.10.31.html#1645700" title="Interior Normal Velocity">Interior Normal Velocity</a></span> but used on an assembly.</div>
    <div class="Head3"><a name="1643925">Normal velocity</a></div>
    <div class="Body_text"><a name="1643926">Select a </a><span class="Menu-Bodytext">Type</span>: <span class="Menu-Bodytext">Inward Velocity</span> (the default) or <span class="Menu-Bodytext">Velocity</span>. </div>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="1643927">For </a><span class="Menu-Bodytext">Inward Velocity </span>enter the value of the <span class="Menu-Bodytext">Inward velocity</span> <span class="EquationVariables">v</span><span class="EquationConstantsSubscript">n</span> (SI unit: m/s).</div>
          </td>
        </tr>
      </table>
    </div>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="1643637">For </a><span class="Menu-Bodytext">Velocity </span>enter values for the components of the <span class="Menu-Bodytext">Velocity</span> <span class="EquationBold">v</span><span class="EquationConstantsSubscript">0</span> (SI unit: m/s).</div>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>