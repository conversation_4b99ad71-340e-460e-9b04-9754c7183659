<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <!-- saved from url=(0014)about:internet -->
    <title>The Thermoviscous Acoustic-Solid Interaction, Frequency Domain Interface</title>
    <link rel="StyleSheet" href="css/aco_ug_thermo.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="css/webworks.css" type="text/css" media="all" />
    <link rel="StyleSheet" href="guide.css" type="text/css" media="all" />
    <script type="text/javascript" language="JavaScript1.2" src="scripts/expand.js"></script>
  </head>
  <body style="margin-bottom: 3px; margin-left: 3px; margin-right: 3px; margin-top: 3px; padding-left: 0px; padding-right: 0px">
    <script type="text/javascript" language="JavaScript1.2">
      <!--
        var  WebWorksRootPath = "";
      // -->
    </script>
    <div class="Head1"><a name="963871">The Thermoviscous Acoustic-Solid Interaction, Frequency Domain Interface</a></div>
    <div class="Body_text"><a name="956740">The </a><span class="Menu-Bodytext">Thermoviscous Acoustic-Solid Interaction, Frequency Domain </span>multiphysics interface (<img class="Default" src="images/aco_ug_thermo.09.37.1.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />), found under the <span class="Menu-Bodytext">Thermoviscous Acoustics </span>branch (<img class="Default" src="images/aco_ug_thermo.09.37.2.png" width="23" height="23" style="display: inline; left: 0.0pt; top: 0.0pt" alt="" />) when adding a physics interface, combines the Thermoviscous Acoustics, Frequency Domain and Solid Mechanics interfaces. The physics interface solves for, and has predefined couplings between, the displacement field in the solid and the acoustic variations in the fluid domains.</div>
    <div class="Body_text"><a name="956747">The physics interface solves the equations in the frequency domain assuming all fields and sources to be harmonic. Linear acoustics is assumed.</a></div>
    <div class="Body_text"><a name="956748">When a predefined </a><span class="Menu-Bodytext">Thermoviscous Acoustic-Solid Interaction, Frequency Domain </span>interface is added from the <span class="Menu-Bodytext">Acoustics&gt;Thermoviscous Acoustics </span>branch of the <span class="Menu-Bodytext">Model Wizard</span> or the <span class="Menu-Bodytext">Add Physics</span> windows, the <span class="Menu-Bodytext">Thermoviscous Acoustics, Frequency Domain </span>and <span class="Menu-Bodytext">Solid Mechanics</span> interfaces are added to the Model Builder.</div>
    <div class="Body_text"><a name="956752">In addition, the </a><span class="Menu-Bodytext">Multiphysics </span>node is added, which automatically includes the multiphysics coupling <span class="Menu-Bodytext">Thermoviscous Acoustic-Structure Boundary</span>. </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="968843"><img class="Default" src="images/aco_ug_thermo.09.37.3.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.00214488189pt">
          <div class="Body_text-cell"><a name="969009">For details about all the multiphysics couplings in the Acoustics Module, see </a><span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.01.html#548506" title="Multiphysics Couplings">Multiphysics Couplings</a></span>.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="956768">Settings for Physics Interfaces and Multiphysics Couplings</a></div>
    <div class="Body_text"><a name="964202">When a predefined multiphysics interface is used, for example </a><span class="Menu-Bodytext">Thermoviscous Acoustic-Solid Interaction, Frequency Domain</span>, the <span class="Menu-Bodytext">Selection </span>on the multiphysics coupling is automatically set to <span class="Menu-Bodytext">All boundaries</span>. In this way, the multiphysics coupling is automatically active on all boundaries with <span class="Menu-Bodytext">Thermoviscous Acoustics, Frequency Domain </span>on one side and <span class="Menu-Bodytext">Solid Mechanics</span> on the other.</div>
    <div class="Body_text"><a name="964203">However, if physics interfaces are added one at a time, followed by the multiphysics couplings, these modified settings are not automatically included.</a></div>
    <div class="Body_text"><a name="964159">For example, if the single interfaces are added, COMSOL Multiphysics adds an empty </a><span class="Menu-Bodytext">Multiphysics</span> node. You can choose the available multiphysics couplings, but you need to manually select on which boundaries they need to be applied, or select <span class="Menu-Bodytext">All boundaries</span> to recover the predefined behavior.</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="956777"><img class="Default" src="images/aco_ug_thermo.09.37.4.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 292.314642519685pt">
          <div class="Body_text"><a name="956779">Multiphysics couplings are available from the context menu (right-click the </a><span class="Menu-Bodytext">Multiphysics</span> node) or from the <span class="Menu-Bodytext">Physics</span> toolbar, <span class="Menu-Bodytext">Multiphysics</span> menu.</div>
        </td>
      </tr>
    </table>
    <div class="Head3"><a name="956809">Physics Interfaces and Multiphysics Couplings</a></div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="956806"><img class="Default" src="images/aco_ug_thermo.09.37.5.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 292.314642519685pt">
          <div class="Body_text-middle-cell"><a name="956808">Use the online help in COMSOL Multiphysics to locate and search all the documentation. All these links also work directly in COMSOL Multiphysics when using the Help system.</a></div>
        </td>
      </tr>
    </table>
    <div class="Head4"><a name="956810">Multiphysics Coupling</a></div>
    <div class="Body_text"><a name="956814">See </a><span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.05.html#627546" title="Thermoviscous Acoustic-Structure Boundary">Thermoviscous Acoustic-Structure Boundary</a></span> in the <span class="Hyperlink"><a href="aco_ug_multiphysics_couplings.13.01.html#548506" title="Multiphysics Couplings">Multiphysics Couplings</a></span> chapter.</div>
    <div class="Head4"><a name="956815">Physics Interface Features</a></div>
    <div class="Body_text"><a name="956816">Physics nodes are available from the </a><span class="Menu-Bodytext">Physics</span> ribbon toolbar (Windows users), <span class="Menu-Bodytext">Physics</span> context menu (Mac or Linux users), or right-click to access the context menu (all users).</div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7583779527559pt">
          <div class="img-tables"><a name="956822"><img class="Default" src="images/aco_ug_thermo.09.37.6.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 292.314642519685pt">
          <div class="Body_text-middle-cell"><a name="956824">In general, to add a node, go to the </a><span class="Menu-Bodytext">Physics</span> toolbar, no matter what operating system you are using. Subnodes are available by clicking the parent node and selecting it from the <span class="Menu-Bodytext">Attributes</span> menu.</div>
        </td>
      </tr>
    </table>
    <div class="Bullets-first_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-first_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-first_inner"><a name="956827">The available physics features for </a><span class="Hyperlink"><a href="aco_ug_thermo.09.02.html#954726" title="The Thermoviscous Acoustics, Frequency Domain Interface">The Thermoviscous Acoustics, Frequency Domain Interface</a></span> are listed in the section <span class="Hyperlink"><a href="aco_ug_thermo.09.03.html#811519" title="Domain, Boundary, and Pair Nodes for the Thermoviscous Acoustics, Frequency Domain Interface">Domain, Boundary, and Pair Nodes for the Thermoviscous Acoustics, Frequency Domain Interface</a></span>.</div>
          </td>
        </tr>
      </table>
    </div>
    <table class="Image-Tables" cellspacing="0" summary="">
      <caption></caption>
      <tr>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-width: 1px; border-right-color: Transparent; border-right-style: solid; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: middle; width: 49.7584062992126pt">
          <div class="img-tables"><a name="979653"><img class="Default" src="images/aco_ug_thermo.09.37.7.png" width="36" height="36" style="display: block; left: 0.0pt; top: 0.0pt" alt="" /></a></div>
        </td>
        <td style="border-bottom-color: LightGrey; border-bottom-style: solid; border-bottom-width: 1px; border-left-color: Silver; border-left-style: solid; border-left-width: 1px; border-right-color: Transparent; border-right-width: 1px; border-top-color: LightGrey; border-top-style: solid; border-top-width: 1px; padding-bottom: 4pt; padding-left: 7pt; padding-right: 7pt; padding-top: 6pt; vertical-align: top; width: 291.283398425197pt">
          <div class="Bullets-inner-cell_outer" style="margin-left: 6.0787401574803095pt">
            <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
              <tr style="vertical-align: baseline">
                <td>
                  <div class="Bullets-inner-cell_inner" style="width: 9.92125984251969pt; white-space: nowrap">•</div>
                </td>
                <td width="100%">
                  <div class="Bullets-inner-cell_inner"><span class="Hyperlink"><a href="aco_ug_thermo.09.46.html#1008569" title="Theory Background for the Thermoviscous Acoustics Branch" name="979658">Theory Background for the Thermoviscous Acoustics Branch</a></span></div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
    <div class="Bullets-last_outer" style="margin-left: 6pt">
      <table border="0" cellspacing="0" cellpadding="0" summary="" role="presentation">
        <tr style="vertical-align: baseline">
          <td>
            <div class="Bullets-last_inner" style="width: 12pt; white-space: nowrap">•</div>
          </td>
          <td width="100%">
            <div class="Bullets-last_inner"><a name="957589">Regarding the available structural-mechanics features and their theory background, see </a><span class="Hyperlink"><a href="aco_ug_sme.14.3.html#1265860" title="The Solid Mechanics Interface">The Solid Mechanics Interface</a></span>.</div>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>